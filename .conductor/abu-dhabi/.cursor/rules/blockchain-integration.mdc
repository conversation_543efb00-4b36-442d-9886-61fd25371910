---
description: 
globs: 
alwaysApply: false
---
# TON区块链集成指南

## 核心区块链服务
- [src/ton-api-service.ts](mdc:src/ton-api-service.ts) - TON API服务包装器
- [src/services/tonService.ts](mdc:src/services/tonService.ts) - TonCenter客户端

## 认证和证明
- [src/routes/tonProof.ts](mdc:src/routes/tonProof.ts) - TON所有权证明
- [src/routes/web3AuthRoutes.ts](mdc:src/routes/web3AuthRoutes.ts) - Web3认证流程

## 钱包集成
- [src/models/UserWallet.ts](mdc:src/models/UserWallet.ts) - 用户钱包模型
- [src/models/MonitorWalletAddress.ts](mdc:src/models/MonitorWalletAddress.ts) - 监控地址
- [src/routes/walletRoutes.ts](mdc:src/routes/walletRoutes.ts) - 钱包API

## 支付和交易
- [src/routes/telegramPaymentRoutes.ts](mdc:src/routes/telegramPaymentRoutes.ts) - Telegram Stars支付
- [src/models/PaymentRequest.ts](mdc:src/models/PaymentRequest.ts) - 支付请求
- [src/models/WalletHistory.ts](mdc:src/models/WalletHistory.ts) - 交易历史

## 提现功能
- [src/routes/withdrawalRoutes.ts](mdc:src/routes/withdrawalRoutes.ts) - 提现API
- [src/models/Withdrawal.ts](mdc:src/models/Withdrawal.ts) - 提现记录
- [src/jobs/withdrawalWorker.ts](mdc:src/jobs/withdrawalWorker.ts) - 提现处理任务

## 代币配置
- [src/models/JettonConfig.ts](mdc:src/models/JettonConfig.ts) - Jetton代币配置
- [src/models/UsdtDepositHistory.ts](mdc:src/models/UsdtDepositHistory.ts) - USDT充值历史

## 集成工具
- [transferTon.js](mdc:transferTon.js) - TON转账脚本
- [src/jobs/integrateTonWithdrawal.ts](mdc:src/jobs/integrateTonWithdrawal.ts) - TON提现集成

## 环境变量配置
需要在`.env`文件中配置:
- `TONCENTER_API_KEY` - TonCenter API密钥
- `TON_WALLET_SEED` - TON钱包种子
- `TON_NETWORK` - 网络环境 (mainnet/testnet)
