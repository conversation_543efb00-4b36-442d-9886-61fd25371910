---
description: 
globs: 
alwaysApply: false
---
# 目录结构指南

## 源代码目录 (src/)
- **routes/** - API路由定义，按功能模块分类
- **controllers/** - 控制器逻辑，处理HTTP请求
- **models/** - Sequelize数据模型定义
- **services/** - 业务逻辑服务层
- **middlewares/** - Express中间件
- **jobs/** - 后台任务和队列处理
- **utils/** - 工具函数
- **config/** - 应用配置
- **types/** - TypeScript类型定义
- **constants/** - 常量定义
- **i18n/** - 国际化文件
- **dto/** - 数据传输对象定义
- **helpers/** - 辅助函数
- **scheduler/** - 定时任务
- **wrappers/** - 外部API包装器

## 根目录重要文件
- **migrations/** - 数据库迁移文件
- **seeders/** - 数据库种子数据
- **docs/** - 项目文档
- **config/** - 外部配置文件
- **scripts/** - 构建和部署脚本
- **backups/** - 备份文件

## 核心文件
- [src/app.ts](mdc:src/app.ts) - 主应用服务器
- [src/bot.ts](mdc:src/bot.ts) - Telegram Bot入口
- [src/models/index.ts](mdc:src/models/index.ts) - 模型关联和导出
