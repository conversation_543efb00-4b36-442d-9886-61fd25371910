# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 常用开发命令

### 启动和运行
```bash
# 开发环境启动（推荐）
npm run dev

# 构建项目
npm run build

# 生产环境启动
npm start

# 数据库迁移和种子数据
npm run seed:tasks

# 离线奖励迁移
npm run migrate:offline-rewards
```

### 环境要求
- Node.js >= 22.0.0
- NPM >= 10.0.0
- MySQL 8.3.0
- Redis 6

### 服务启动
```bash
# 使用启动脚本（自动检查Node.js版本）
./start.sh

# Docker环境启动
docker-compose up -d
```

### 快速测试
```bash
# 快速健康检查
node quick_test.js

# 完整API测试
node test_reset_api.js
```

## 架构概述

### 技术栈
- **后端**: TypeScript + Express.js
- **数据库**: MySQL (Sequelize ORM) + Redis
- **任务队列**: BullMQ
- **数值计算**: BigNumber.js（高精度计算）

### 项目结构
```
src/
├── app.ts                  # 主应用入口
├── config/                 # 配置文件
├── controllers/            # 控制器层
├── services/               # 业务逻辑层
├── models/                 # 数据模型
├── routes/                 # 路由定义
├── middlewares/            # 中间件
├── jobs/                   # 后台任务
├── utils/                  # 工具函数
└── types/                  # TypeScript类型定义
```

## 核心系统架构

### 1. 游戏核心循环
游戏遵循"牛奶生产 → 宝石转换 → 设施升级"的核心循环：

- **牧场区系统** (`FarmPlot`): 牛奶生产设施，包含20个可升级区域
- **配送线系统** (`DeliveryLine`): 将牛奶打包成方块并转换为宝石
- **核心游戏循环** (`gameLoopService`): 整合两个系统，实现离线收益计算

### 2. 用户系统
- **UserWallet**: 用户钱包，存储宝石和牛奶等资源
- **认证系统**: Web3Auth认证
- **多语言支持**: 中文、英文、日文 (i18n)

### 3. 任务和奖励系统
- **任务系统** (`Tasks`): 用户日常任务和成就
- **宝箱系统** (`Chest`): 奖励机制
- **推荐系统**: 邀请奖励机制
- **排行榜**: 宝石排名系统

### 4. 支付和IAP
- **应用内购买** (`IapProduct`, `IapPurchase`): 支持各种增强道具
- **DApp Portal**: 门户网站支付

## 数据库设计要点

### 核心模型关系
- `UserWallet` ← 一对多 → `FarmPlot`
- `UserWallet` ← 一对一 → `DeliveryLine`
- `UserWallet` ← 一对多 → `Chest`
- `UserWallet` ← 一对多 → `Tasks`

### 精度处理
项目使用BigNumber.js处理所有数值计算，确保精度：
```typescript
import { BigNumber } from 'bignumber.js';
// 在 src/utils/bigNumberConfig.ts 中配置全局精度
```

## 开发指南

### 添加新功能
1. 在 `src/models/` 创建数据模型
2. 在 `src/services/` 实现业务逻辑
3. 在 `src/controllers/` 添加控制器
4. 在 `src/routes/` 定义路由
5. 运行迁移更新数据库

### 测试重置API
项目包含完整的测试重置功能（仅开发环境）:
- 端点: `/api/test/reset-game-state`
- 包含JWT认证和速率限制
- 详细的审计日志记录

### 离线奖励系统
关键实现在 `accumulatedOfflineRewardService.ts`:
- 计算用户离线期间的牛奶产出
- 处理配送线的宝石转换
- 应用VIP和增强道具效果

## 环境配置

### 必需环境变量
```env
NODE_ENV=development
DB_HOST=localhost
DB_NAME=wolf_fun
REDIS_HOST=localhost
JWT_SECRET=your_secret
```

### 端口配置
- 应用服务器: 3456
- MySQL: 3669
- Redis: 6257
- phpMyAdmin: 8269
- RedisInsight: 5577

## 关键代码位置

### 游戏逻辑
- 牧场区逻辑: `src/services/farmPlotService.ts`
- 配送线逻辑: `src/services/deliveryLineService.ts`
- 游戏循环: `src/services/gameLoopService.ts`
- 离线奖励: `src/services/accumulatedOfflineRewardService.ts`

### 用户管理
- 钱包服务: `src/services/walletService.ts`
- 认证中间件: `src/middlewares/walletAuth.ts`
- 用户服务: `src/services/userService.ts`

### 批量资源更新
- 严格验证API: `src/services/strictBatchResourceUpdateService.ts`
- 批量更新: `src/services/batchResourceUpdateService.ts`

## 重要说明

- 所有数值计算必须使用BigNumber.js确保精度
- 测试重置API仅在开发环境可用，有严格的安全检查
- 项目要求Node.js 22+，旧版本可能导致兼容性问题
- 数据库迁移前请备份数据