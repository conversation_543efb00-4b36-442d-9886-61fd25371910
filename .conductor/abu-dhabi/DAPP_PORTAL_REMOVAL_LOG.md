# DappPortal代码移除日志

## 移除日期
2025-07-17

## 移除原因
替换DappPortal支付方式为PHRS代币支付系统

## 移除的文件和代码

### 1. 控制器文件
- `src/controllers/dappPortalPaymentController.ts` - DappPortal支付控制器

### 2. 路由文件
- `src/routes/dappPortalPaymentRoutes.ts` - DappPortal支付路由

### 3. 定时任务文件
- `src/scheduler/paymentStatusUpdater.ts` - DappPortal支付状态更新定时任务

### 4. 文档文件
- `README-dapp-portal-payment.md` - DappPortal支付集成说明文档

### 5. 需要修改的文件

#### src/controllers/iapController.ts
- 移除DappPortal支付创建逻辑
- 移除DappPortal相关的环境变量使用
- 移除DappPortal API调用代码

#### src/app.ts
- 移除dappPortalPaymentRoutes导入和使用

#### 环境变量
- DAPP_PORTAL_CLIENT_ID
- DAPP_PORTAL_CLIENT_SECRET

## 替换方案
- 使用PHRS代币支付系统
- 新的API接口：`/api/phrs-payment/purchase`
- 新的控制器：`phrsPaymentController`

## 备注
- 所有移除的代码已备份到此日志文件中
- 数据库中的IapPurchase表保留，但paymentMethod字段将使用'phrs'而不是'kaia'或'stripe'
- 现有的购买记录不受影响，只是不再创建新的DappPortal支付记录
