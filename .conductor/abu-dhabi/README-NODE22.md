# Wolf Fun 游戏服务器 - Node.js 22 版本

## 🚀 快速开始

### 前置要求

- **Node.js 22.0.0 或更高版本**
- **NPM 10.0.0 或更高版本**
- MySQL 数据库
- Redis 服务器

### 安装和启动

#### 方法 1: 使用启动脚本（推荐）

```bash
# 克隆项目
git clone <repository-url>
cd wolf_fun

# 使用启动脚本（自动处理 Node.js 版本）
./start.sh
```

#### 方法 2: 手动启动

```bash
# 1. 确保使用 Node.js 22
nvm use 22
# 或者
nvm install 22 && nvm use 22

# 2. 安装依赖
npm install

# 3. 启动开发服务器
npm run dev
```

### Node.js 版本管理

项目已配置为使用 Node.js 22：

- **`.nvmrc`**: 指定 Node.js 版本为 22
- **`package.json`**: engines 字段要求 Node.js >= 22.0.0
- **`start.sh`**: 启动脚本自动检查和切换版本

### 验证安装

服务器启动后，访问以下地址验证：

```bash
# 健康检查
curl http://localhost:3456/api/test/health

# 预期响应
{
  "ok": true,
  "data": {
    "status": "ok",
    "environment": "development",
    "isDevelopment": true,
    "timestamp": "2025-06-19T08:17:59.122Z",
    "uptime": 35.518942167
  }
}
```

### 测试重置 API

项目包含完整的测试重置 API 功能：

```bash
# 运行快速测试（无需 JWT token）
node quick_test.js

# 运行完整测试（需要 JWT token）
# 先修改 test_reset_api.js 中的 token
node test_reset_api.js
```

### 环境配置

确保 `.env` 文件包含必要的配置：

```env
NODE_ENV=development
DB_HOST=localhost
DB_PORT=3306
DB_NAME=wolf_fun
DB_USER=your_username
DB_PASS=your_password
REDIS_HOST=localhost
REDIS_PORT=6379
JWT_SECRET=your_jwt_secret
```

### 项目结构

```
wolf_fun/
├── src/
│   ├── services/testResetService.ts    # 测试重置服务
│   ├── controllers/testResetController.ts  # 测试重置控制器
│   ├── routes/testResetRoutes.ts       # 测试重置路由
│   └── ...
├── docs/
│   └── test-reset-api.md              # API 文档
├── .nvmrc                             # Node.js 版本配置
├── start.sh                           # 启动脚本
├── quick_test.js                      # 快速测试脚本
├── test_reset_api.js                  # 完整测试脚本
└── README-NODE22.md                   # 本文档
```

### 常见问题

#### Q: 如何检查当前 Node.js 版本？
```bash
node --version
```

#### Q: 如何安装 Node.js 22？
```bash
# 使用 nvm
nvm install 22
nvm use 22

# 或者从官网下载
# https://nodejs.org/
```

#### Q: 启动时出现 TypeScript 错误？
确保使用 Node.js 22，旧版本不支持某些 TypeScript 特性。

#### Q: 端口 3456 被占用？
```bash
# 查看占用进程
lsof -i :3456

# 或者修改端口
PORT=3000 npm run dev
```

#### Q: 如何停止服务器？
在终端中按 `Ctrl+C`

### 开发工具

- **TypeScript**: 5.7.3
- **ts-node-dev**: 热重载开发
- **Express**: Web 框架
- **Sequelize**: ORM
- **Redis**: 缓存和队列
- **BullMQ**: 任务队列

### 测试重置 API 功能

- ✅ JWT 身份验证
- ✅ 速率限制（每分钟 5 次）
- ✅ 详细审计日志
- ✅ 环境信息记录
- ✅ 农场区块重置
- ✅ 配送线重置
- ✅ BigNumber.js 精确计算
- ✅ 国际化支持

### API 端点

- `GET /api/test/health` - 健康检查
- `GET /api/test/reset-safety-info` - 重置安全检查信息
- `POST /api/test/reset-game-state` - 重置游戏状态

### 支持

如有问题，请查看：
1. `docs/test-reset-api.md` - 详细 API 文档
2. 运行 `node quick_test.js` 进行基本功能测试
3. 检查服务器日志输出

---

**注意**: 测试重置 API 会永久删除游戏数据，请谨慎使用。所有操作都会被详细记录。
