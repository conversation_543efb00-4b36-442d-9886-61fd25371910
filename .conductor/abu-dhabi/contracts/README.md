# PHRS充值智能合约

基于Pharos网络的PHRS原生币充值智能合约，支持透明代理升级模式。

## 🔄 重要变更

### PHRS现在是原生币
- **之前**: PHRS是ERC20代币，需要代币合约地址
- **现在**: PHRS是Pharos网络的原生币（类似以太坊的ETH）
- **影响**: 用户直接发送原生币到合约，不需要代币授权

### 透明代理升级
- 使用OpenZeppelin的透明代理模式
- 升级逻辑在代理合约中，不在实现合约中
- 用户始终与代理合约地址交互
- 实现合约地址在升级时会改变
- 通过ProxyAdmin合约管理升级权限

### 简化的功能
- 移除了充值记录存储功能
- 只保留充值事件发射
- 后端通过监听事件来更新用户余额

### 安全改进
- 禁止直接向合约地址发送原生币
- 必须通过deposit()函数显式充值
- receive()和fallback()函数会拒绝直接转账

## 📋 合约功能

### 核心功能
- ✅ 接收PHRS原生币充值
- ✅ 充值金额限制检查
- ✅ 发射充值事件供后端监听
- ✅ 管理员控制（暂停/恢复）
- ✅ 紧急提取功能
- ✅ 透明代理升级支持

### 安全特性
- ✅ 重入攻击防护
- ✅ 暂停机制
- ✅ 所有者权限控制
- ✅ 参数验证
- ✅ 升级授权检查

## �� 快速开始

### 1. 安装依赖
```bash
npm install
```

### 2. 配置环境变量
```bash
cp .env.example .env
# 编辑 .env 文件，填入实际配置
```

### 3. 编译合约
```bash
npm run compile
```

### 4. 运行测试
```bash
npm test
```

### 5. 部署到测试网
```bash
npm run deploy:testnet
```

### 6. 部署到主网
```bash
npm run deploy:mainnet
```

## 🔧 部署和升级

### 初始部署
```bash
# 测试网部署
npm run deploy:testnet

# 主网部署
npm run deploy:mainnet
```

### 合约升级
```bash
# 测试网升级
npm run upgrade:testnet

# 主网升级
npm run upgrade:mainnet
```

## 🔄 迁移指南

从旧版本迁移：
1. 更新后端配置，移除PHRS_TOKEN_ADDRESS
2. 使用代理合约地址替代旧合约地址
3. 更新事件监听逻辑（事件结构已简化）
4. 测试充值流程确保正常工作
