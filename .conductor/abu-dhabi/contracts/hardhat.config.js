require("@nomicfoundation/hardhat-toolbox");
require("@openzeppelin/hardhat-upgrades");
require("dotenv").config();

/** @type import('hardhat/config').HardhatUserConfig */
module.exports = {
  solidity: {
    version: "0.8.22",
    settings: {
      optimizer: {
        enabled: true,
        runs: 200
      }
    }
  },
  networks: {
    hardhat: {
      chainId: 1337
    },
    pharos_testnet: {
      url: process.env.PHAROS_TESTNET_RPC_URL || "https://testnet.dplabs-internal.com",
      accounts: process.env.PRIVATE_KEY ? [process.env.PRIVATE_KEY] : [],
      chainId: 688688, // Pharos测试网链ID，需要根据实际情况调整
    },
    pharos_mainnet: {
      url: process.env.PHAROS_MAINNET_RPC_URL || "https://rpc.pharos.network",
      accounts: process.env.PRIVATE_KEY ? [process.env.PRIVATE_KEY] : [],
      chainId: 1000, // Pharos主网链ID，需要根据实际情况调整
      gasPrice: ***********, // 20 gwei
      gas: 8000000
    }
  },
  etherscan: {
    customChains: [
      {
        network: "pharos_testnet",
        chainId: 688688,
        urls: {
          apiURL: process.env.PHAROS_TESTNET_EXPLORER_API || "https://api.socialscan.io/pharos-testnet/v1/explorer/command_api/contract",
          browserURL: process.env.PHAROS_TESTNET_EXPLORER || "https://testnet.pharosscan.xyz/"
        }
      },
      {
        network: "pharos_mainnet",
        chainId: 1000,
        urls: {
          apiURL: process.env.PHAROS_MAINNET_EXPLORER_API || "https://explorer.pharos.network/api",
          browserURL: process.env.PHAROS_MAINNET_EXPLORER || "https://explorer.pharos.network"
        }
      }
    ],
    apiKey: {
      pharos: "Put a random string", // Note we don't need a apiKey here, just leave a random string
    },
  },
  gasReporter: {
    enabled: process.env.REPORT_GAS !== undefined,
    currency: "USD"
  }
};
