const { ethers, upgrades } = require("hardhat");
const fs = require("fs");
const path = require("path");

async function main() {
  console.log("开始部署PHRS充值合约（透明代理模式）...");

  // 获取部署账户
  const [deployer] = await ethers.getSigners();
  console.log("部署账户:", deployer.address);
  console.log("账户余额:", ethers.formatEther(await deployer.provider.getBalance(deployer.address)));

  // 合约参数配置（PHRS现在是原生币，不需要代币地址）
  const MIN_DEPOSIT_AMOUNT = ethers.parseEther(process.env.MIN_DEPOSIT_AMOUNT || "0.00001"); // 默认1 PHRS
  const MAX_DEPOSIT_AMOUNT = ethers.parseEther(process.env.MAX_DEPOSIT_AMOUNT || "1000000000000000"); // 默认10000 PHRS

  console.log("合约参数:");
  console.log("- 最小充值金额:", ethers.formatEther(MIN_DEPOSIT_AMOUNT), "PHRS");
  console.log("- 最大充值金额:", ethers.formatEther(MAX_DEPOSIT_AMOUNT), "PHRS");

  // 部署透明代理合约
  console.log("\n部署实现合约和透明代理...");
  const PHRSDepositContract = await ethers.getContractFactory("PHRSDepositContract");

  const contract = await upgrades.deployProxy(
    PHRSDepositContract,
    [MIN_DEPOSIT_AMOUNT, MAX_DEPOSIT_AMOUNT],
    {
      initializer: 'initialize',
      kind: 'transparent'
    }
  );

  await contract.waitForDeployment();
  const proxyAddress = await contract.getAddress();
  const implementationAddress = await upgrades.erc1967.getImplementationAddress(proxyAddress);

  console.log("✅ PHRS充值合约部署成功!");
  console.log("代理合约地址:", proxyAddress);
  console.log("实现合约地址:", implementationAddress);
  console.log("交易哈希:", contract.deploymentTransaction().hash);

  // 验证合约部署
  console.log("\n验证合约部署...");
  const contractInfo = await contract.getContractInfo();
  console.log("合约信息验证:");
  console.log("- 最小充值金额:", ethers.formatEther(contractInfo[0]), "PHRS");
  console.log("- 最大充值金额:", ethers.formatEther(contractInfo[1]), "PHRS");
  console.log("- 合约余额:", ethers.formatEther(contractInfo[2]), "PHRS");

  // 保存部署信息
  const deploymentInfo = {
    network: hre.network.name,
    proxyAddress: proxyAddress,
    implementationAddress: implementationAddress,
    deployerAddress: deployer.address,
    minDepositAmount: ethers.formatEther(MIN_DEPOSIT_AMOUNT),
    maxDepositAmount: ethers.formatEther(MAX_DEPOSIT_AMOUNT),
    deploymentTime: new Date().toISOString(),
    transactionHash: contract.deploymentTransaction().hash,
    blockNumber: contract.deploymentTransaction().blockNumber,
    isUpgradeable: true,
    proxyKind: 'transparent'
  };

  const deploymentPath = path.join(__dirname, "..", "deployments");
  if (!fs.existsSync(deploymentPath)) {
    fs.mkdirSync(deploymentPath, { recursive: true });
  }

  const deploymentFile = path.join(deploymentPath, `${hre.network.name}.json`);
  fs.writeFileSync(deploymentFile, JSON.stringify(deploymentInfo, null, 2));

  console.log(`\n部署信息已保存到: ${deploymentFile}`);

  // 生成ABI文件
  const artifactPath = path.join(__dirname, "..", "artifacts", "contracts", "PHRSDepositContract.sol", "PHRSDepositContract.json");
  if (fs.existsSync(artifactPath)) {
    const artifact = JSON.parse(fs.readFileSync(artifactPath, "utf8"));
    const abiPath = path.join(deploymentPath, `PHRSDepositContract-${hre.network.name}.abi.json`);
    fs.writeFileSync(abiPath, JSON.stringify(artifact.abi, null, 2));
    console.log(`ABI文件已保存到: ${abiPath}`);
  }

  console.log("\n🎉 部署完成!");
  console.log("请将以下信息添加到后端环境变量:");
  console.log(`PHRS_DEPOSIT_CONTRACT_ADDRESS=${proxyAddress}`);
  console.log("\n重要提示:");
  console.log("- 用户应该与代理合约地址交互，不是实现合约地址");
  console.log("- PHRS现在是原生币，不需要代币合约地址");
  console.log("- 合约使用透明代理模式，可以通过ProxyAdmin升级");

  if (hre.network.name !== "hardhat") {
    console.log("\n等待区块确认后，可以使用以下命令验证代理合约:");
    console.log(`npx hardhat verify --network ${hre.network.name} ${proxyAddress}`);
    console.log("\n验证实现合约:");
    console.log(`npx hardhat verify --network ${hre.network.name} ${implementationAddress}`);
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error("部署失败:", error);
    process.exit(1);
  });
