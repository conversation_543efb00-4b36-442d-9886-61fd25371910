const { ethers } = require("hardhat");

async function main() {
  console.log("🚀 PHRS充值快速测试");
  console.log("===================");

  // 配置参数
  const DEPOSIT_AMOUNT = "1.0"; // 充值金额 (PHRS)
  const CONTRACT_ADDRESS = process.env.PHRS_DEPOSIT_CONTRACT_ADDRESS || ""; // 从环境变量获取

  if (!CONTRACT_ADDRESS) {
    console.log("❌ 请设置 PHRS_DEPOSIT_CONTRACT_ADDRESS 环境变量");
    console.log("   export PHRS_DEPOSIT_CONTRACT_ADDRESS=0x...");
    process.exit(1);
  }

  try {
    // 1. 获取签名者
    const [signer] = await ethers.getSigners();
    console.log(`👤 测试账户: ${signer.address}`);

    // 2. 检查余额
    const balance = await ethers.provider.getBalance(signer.address);
    console.log(`💰 账户余额: ${ethers.formatEther(balance)} PHRS`);

    // 3. 连接合约
    const contractABI = [
      "function deposit() external payable",
      "function getContractInfo() external view returns (uint256, uint256, uint256, uint256)",
      "function getUserBalance(address user) external view returns (uint256)",
      "event Deposit(address indexed user, uint256 amount, uint256 timestamp)"
    ];

    const contract = new ethers.Contract(CONTRACT_ADDRESS, contractABI, signer);
    console.log(`📋 合约地址: ${CONTRACT_ADDRESS}`);

    // 4. 获取充值前状态
    console.log("\n📊 充值前状态:");
    const beforeInfo = await contract.getContractInfo();
    const beforeUserBalance = await contract.getUserBalance(signer.address);
    
    console.log(`   合约总余额: ${ethers.formatEther(beforeInfo[2])} PHRS`);
    console.log(`   合法充值总额: ${ethers.formatEther(beforeInfo[3])} PHRS`);
    console.log(`   用户链上余额: ${ethers.formatEther(beforeUserBalance)} PHRS`);

    // 5. 执行充值
    console.log(`\n💸 开始充值 ${DEPOSIT_AMOUNT} PHRS...`);
    const depositAmount = ethers.parseEther(DEPOSIT_AMOUNT);
    
    const tx = await contract.deposit({ 
      value: depositAmount,
      gasLimit: 200000
    });

    console.log(`📝 交易哈希: ${tx.hash}`);
    console.log("⏳ 等待确认...");

    const receipt = await tx.wait();
    console.log(`✅ 交易确认! 区块: ${receipt.blockNumber}`);

    // 6. 获取充值后状态
    console.log("\n📊 充值后状态:");
    const afterInfo = await contract.getContractInfo();
    const afterUserBalance = await contract.getUserBalance(signer.address);
    
    console.log(`   合约总余额: ${ethers.formatEther(afterInfo[2])} PHRS`);
    console.log(`   合法充值总额: ${ethers.formatEther(afterInfo[3])} PHRS`);
    console.log(`   用户链上余额: ${ethers.formatEther(afterUserBalance)} PHRS`);

    // 7. 验证变化
    const contractBalanceChange = afterInfo[2] - beforeInfo[2];
    const legitimateChange = afterInfo[3] - beforeInfo[3];
    const userBalanceChange = afterUserBalance - beforeUserBalance;

    console.log("\n🔍 变化验证:");
    console.log(`   合约余额变化: +${ethers.formatEther(contractBalanceChange)} PHRS`);
    console.log(`   合法充值变化: +${ethers.formatEther(legitimateChange)} PHRS`);
    console.log(`   用户余额变化: +${ethers.formatEther(userBalanceChange)} PHRS`);

    if (contractBalanceChange === depositAmount && 
        legitimateChange === depositAmount && 
        userBalanceChange === depositAmount) {
      console.log("🎉 充值成功，数据一致!");
    } else {
      console.log("⚠️  数据不一致，请检查!");
    }

    console.log("\n✅ 测试完成!");

  } catch (error) {
    console.error("❌ 测试失败:", error.message);
    if (error.reason) {
      console.error("失败原因:", error.reason);
    }
    process.exit(1);
  }
}

main().catch((error) => {
  console.error("脚本执行失败:", error);
  process.exit(1);
});
