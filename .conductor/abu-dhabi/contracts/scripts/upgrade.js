const { ethers, upgrades } = require("hardhat");
const fs = require("fs");
const path = require("path");

async function main() {
  console.log("开始升级PHRS充值合约（透明代理模式）...");

  // 获取部署账户
  const [deployer] = await ethers.getSigners();
  console.log("升级账户:", deployer.address);
  console.log("账户余额:", ethers.formatEther(await deployer.provider.getBalance(deployer.address)));

  // 从部署文件读取代理地址
  const deploymentPath = path.join(__dirname, "..", "deployments");
  const deploymentFile = path.join(deploymentPath, `${hre.network.name}.json`);
  
  if (!fs.existsSync(deploymentFile)) {
    throw new Error(`部署文件不存在: ${deploymentFile}`);
  }

  const deploymentInfo = JSON.parse(fs.readFileSync(deploymentFile, "utf8"));
  const proxyAddress = deploymentInfo.proxyAddress;

  if (!proxyAddress) {
    throw new Error("部署文件中未找到代理合约地址");
  }

  console.log("代理合约地址:", proxyAddress);

  // 获取新的实现合约工厂
  const PHRSDepositContractV2 = await ethers.getContractFactory("PHRSDepositContract");

  console.log("\n开始升级合约...");
  
  // 升级合约
  const upgradedContract = await upgrades.upgradeProxy(proxyAddress, PHRSDepositContractV2);
  await upgradedContract.waitForDeployment();

  const newImplementationAddress = await upgrades.erc1967.getImplementationAddress(proxyAddress);

  console.log("✅ 合约升级成功!");
  console.log("代理合约地址:", proxyAddress);
  console.log("新实现合约地址:", newImplementationAddress);

  // 验证升级后的合约
  console.log("\n验证升级后的合约...");
  const contractInfo = await upgradedContract.getContractInfo();
  console.log("合约信息验证:");
  console.log("- 最小充值金额:", ethers.formatEther(contractInfo[0]), "PHRS");
  console.log("- 最大充值金额:", ethers.formatEther(contractInfo[1]), "PHRS");
  console.log("- 合约余额:", ethers.formatEther(contractInfo[2]), "PHRS");

  // 更新部署信息
  const upgradeInfo = {
    ...deploymentInfo,
    lastUpgrade: {
      implementationAddress: newImplementationAddress,
      upgradeTime: new Date().toISOString(),
      upgraderAddress: deployer.address,
      transactionHash: upgradedContract.deploymentTransaction()?.hash
    }
  };

  fs.writeFileSync(deploymentFile, JSON.stringify(upgradeInfo, null, 2));
  console.log(`\n升级信息已保存到: ${deploymentFile}`);

  console.log("\n🎉 升级完成!");
  console.log("代理合约地址保持不变:", proxyAddress);
  console.log("新实现合约地址:", newImplementationAddress);
  
  if (hre.network.name !== "hardhat") {
    console.log("\n等待区块确认后，可以使用以下命令验证新实现合约:");
    console.log(`npx hardhat verify --network ${hre.network.name} ${newImplementationAddress}`);
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error("升级失败:", error);
    process.exit(1);
  });
