# Wolf Fun 项目需求文档

## 1. 项目概述

### 1.1 项目背景

Wolf Fun 是一个基于区块链技术的游戏平台，结合了 TON 区块链、电报机器人和游戏元素，为用户提供丰富的互动体验和奖励机制。平台通过宝箱系统、奖励机制、任务系统和游戏玩法，鼓励用户参与并获得数字资产奖励。

### 1.2 项目目标

- 构建一个基于 TON 区块链的游戏生态系统
- 通过电报机器人提供便捷的用户交互界面
- 实现多样化的奖励机制，增强用户粘性
- 建立完善的任务和游戏系统，提高用户参与度
- 提供安全可靠的数字资产管理和提现功能

## 2. 用户资产系统

### 2.1 用户钱包

用户钱包是用户在平台上的资产管理中心，包含以下资产类型：

- **TON**：平台主要加密货币，可用于游戏和提现
- **Gem**：平台内部通用积分，可通过任务和宝箱获得
- **Ticket**：游戏门票，分为普通门票和免费门票
- **Fragment**：碎片，分为绿色、蓝色、紫色和金色四种，可用于合成奖励
- **MOOF**：平台代币，可解锁特殊功能

### 2.2 提现系统

平台支持多种数字资产的提现功能：

- **TON 提现**：最低提现金额 1 TON，手续费 0.1 TON
- **USD 提现**：最低提现金额 10 USD，手续费 1 USD
- **MOOF 提现**：最低提现金额 21 MOOF，手续费 10 MOOF

提现系统包含安全审核机制，超过特定阈值的提现需要手动审核。

## 3. 宝箱系统

### 3.1 倒计时宝箱

- 用户可以每隔固定时间（默认 24 小时）领取一个倒计时宝箱
- 宝箱包含随机奖励，如 Gem、Fragment 等
- Star 用户可以开启自动领取功能

### 3.2 Jackpot 宝箱

平台设有三级 Jackpot 奖池：

- **一级奖池**：目标金额 10 TON
- **二级奖池**：目标金额 20 TON
- **三级奖池**：目标金额 50 TON

奖池资金来源：
- 新用户注册贡献：0.01 TON/用户
- 开宝箱贡献：0.0001 TON/次

### 3.3 助力系统

用户可以通过分享链接获得助力，加速宝箱倒计时：

- 分享链接有效期为 24 小时，最多可被使用 12 次
- 不同级别的宝箱有不同的助力奖励
- 推荐系统提供额外的加速奖励

## 4. 任务系统

### 4.1 任务类型

- **日常任务**：每日可完成的任务，如签到
- **一次性任务**：只能完成一次的任务，如加入电报频道
- **周期性任务**：按周期重置的任务

### 4.2 任务奖励

完成任务可获得各种奖励：
- Gem
- Fragment
- Ticket
- 宝箱
- TON

## 5. 游戏系统

### 5.1 游戏机制

- 用户使用 Ticket 或 Free Ticket 参与游戏
- 游戏分为多个房间，每个房间有不同的下注金额
- 游戏结果记录在 GameHistory 中

### 5.2 奖励机制

- 游戏胜利可获得相应的 TON 奖励
- 特定游戏可能有额外的 MOOF 奖励
- 游戏参与可能触发特殊宝箱奖励

## 6. 社交和推广系统

### 6.1 推荐机制

- 用户可以通过推荐链接邀请新用户
- 成功推荐新用户可获得推荐奖励
- 推荐用户数量会影响某些功能的解锁

### 6.2 电报集成

- 平台与电报机器人深度集成
- 支持电报支付功能
- 电报 Star 用户享有特殊权益

## 7. 技术架构

### 7.1 后端架构

- **编程语言**：TypeScript
- **框架**：Express.js
- **数据库**：MySQL (Sequelize ORM)
- **缓存**：Redis
- **任务队列**：Bull MQ

### 7.2 区块链集成

- TON 区块链集成
- 支持钱包地址验证和交易
- 区块链交易监控系统

### 7.3 API 接口

平台提供丰富的 API 接口，包括：

- 用户认证和钱包管理 API
- 宝箱和奖励系统 API
- 任务完成和验证 API
- 游戏参与和结果 API
- 提现和交易 API

## 8. 安全机制

### 8.1 用户认证

- 钱包地址认证
- 电报认证
- 交易签名验证

### 8.2 资金安全

- 交易多重确认机制
- 大额提现手动审核
- 交易记录完整追踪

## 9. 监控和运维

### 9.1 系统监控

- 服务器状态监控
- API 性能监控
- 区块链交易监控

### 9.2 数据备份

- 数据库定期备份
- 交易记录备份
- 用户资产状态备份

## 10. 未来规划

### 10.1 功能扩展

- 更多游戏类型
- 社区治理机制
- NFT 集成

### 10.2 生态扩展

- 多链支持
- 跨平台集成
- 开发者 API