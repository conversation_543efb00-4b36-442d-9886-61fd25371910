# PHRS历史交易查询功能

## 🎯 功能概述

PHRS监控服务现在支持在启动时查询和处理历史交易，确保不会遗漏任何充值记录。

## 🔧 配置方式

### 1. 环境变量配置

在 `.env` 文件中设置历史起始区块：

```bash
# PHRS历史交易处理起始区块号
PHRS_HISTORICAL_START_BLOCK=13805521
```

### 2. 动态配置

也可以在启动时通过命令行参数指定：

```bash
npm run start:historical -- 13805521
```

## 🚀 使用方法

### 方法1：环境变量配置启动

1. **设置环境变量**：
   ```bash
   echo "PHRS_HISTORICAL_START_BLOCK=13805521" >> .env
   ```

2. **启动应用**：
   ```bash
   npm start
   ```

3. **监控服务将自动**：
   - 从区块13805521开始处理所有历史交易
   - 分批处理，每批5000个区块
   - 处理完历史交易后，继续监控新交易

### 方法2：命令行启动

```bash
npm run start:historical -- 13805521
```

这将：
- 临时设置历史起始区块为13805521
- 启动监控服务
- 显示处理进度
- 每分钟显示状态更新

### 方法3：交互式测试

```bash
npm run test:historical
```

提供交互式界面，可以：
- 清空数据库并从指定区块开始处理
- 从当前进度继续处理
- 查询指定区块范围的交易
- 显示统计信息

## 📊 处理逻辑

### 启动时的处理流程

1. **加载配置**：
   - 读取 `PHRS_HISTORICAL_START_BLOCK` 环境变量
   - 如果未设置或设置为0，跳过历史交易处理

2. **历史事件处理判断**：
   - 如果 `PHRS_HISTORICAL_START_BLOCK > 0`：处理历史交易
   - 如果 `PHRS_HISTORICAL_START_BLOCK = 0` 或未设置：跳过历史交易，直接从当前区块开始监控

3. **确定起始区块**（仅当需要处理历史交易时）：
   - 如果数据库中有记录：使用最后处理的区块号+1
   - 如果数据库为空且配置了历史起始区块：使用配置的起始区块
   - 如果都没有：从当前区块开始

4. **分批处理历史交易**（仅当需要处理历史交易时）：
   - 每批处理1000个区块（避免RPC限制）
   - 显示处理进度
   - 自动处理错误和重试

5. **继续实时监控**：
   - 历史交易处理完成后（或跳过历史交易处理）
   - 每10秒检查新交易

### 处理示例

```
🔍 开始处理历史PHRS充值事件，从区块 13805521 到 13824605
   配置的历史起始区块: 13805521
   最后处理区块: 0

📦 处理批次 1: 区块 13805521 到 13810520
   找到 1 个充值事件
   ✅ 批次 1 处理完成，已处理到区块 13810520

📦 处理批次 2: 区块 13810521 到 13815520
   找到 0 个充值事件
   ✅ 批次 2 处理完成，已处理到区块 13815520

...

🎉 历史事件处理完成！
   总批次数: 4
   总处理事件数: 1
   最终处理到区块: 13824605
```

## 🛠️ 高级功能

### 1. 重新处理历史交易

如果需要重新处理某个区块范围的交易：

```bash
# 清空数据库并重新处理
npm run test:historical
# 选择选项1，输入起始区块号
```

### 2. 查询特定区块范围

```bash
# 查询特定区块范围（不保存到数据库）
npm run process:block -- 13805521
```

### 3. 检查处理状态

```bash
# 检查充值记录
npm run check:phrs-deposits

# 检查监控服务状态
npm run diagnose:phrs-monitor
```

## 📋 配置参数说明

| 参数 | 说明 | 默认值 | 示例 |
|------|------|--------|------|
| `PHRS_HISTORICAL_START_BLOCK` | 历史交易处理起始区块号 | 0 | 13805521 |
| `PHRS_DEPOSIT_CONTRACT_ADDRESS` | PHRS充值合约地址 | 必需 | 0x4339... |
| `PHAROS_RPC_URL` | Pharos网络RPC地址 | 默认测试网 | https://... |

## ⚠️ 注意事项

### 1. 性能考虑

- **大区块范围**：处理大量历史区块可能需要较长时间
- **RPC限制**：单次查询限制为5000个区块
- **数据库性能**：大量插入操作可能影响数据库性能

### 2. 网络稳定性

- **RPC超时**：网络不稳定可能导致查询失败
- **自动重试**：服务会自动处理错误并继续
- **断点续传**：可以从中断的地方继续处理

### 3. 数据一致性

- **重复处理**：已处理的交易会被自动跳过
- **事务安全**：使用数据库事务确保数据一致性
- **错误处理**：失败的交易会被记录但不会阻止处理

## 🔍 故障排除

### 常见问题

1. **"区块范围过大"错误**：
   - 原因：RPC节点限制
   - 解决：自动分批处理，无需手动干预

2. **"起始区块超过当前区块"**：
   - 原因：配置的起始区块号太大
   - 解决：检查并修正 `PHRS_HISTORICAL_START_BLOCK` 配置

3. **处理速度慢**：
   - 原因：网络延迟或大量交易
   - 解决：耐心等待，服务会显示进度

### 调试命令

```bash
# 诊断监控服务
npm run diagnose:phrs-monitor

# 检查区块范围限制
npm run test:block-range

# 查看充值记录统计
npm run check:phrs-deposits
```

## 📊 监控和日志

### 日志输出示例

```
PHRS充值服务初始化完成 - 合约地址: 0x4339991F8ce06080bF8F2095ecf6fBc3d6c2B238
历史事件处理起始区块: 13805521
开始监听PHRS充值事件，从区块 13805521 开始
🔍 开始处理历史PHRS充值事件，从区块 13805521 到 13824605
📦 处理批次 1: 区块 13805521 到 13810520
   找到 1 个充值事件
   ✅ 批次 1 处理完成，已处理到区块 13810520
🎉 历史事件处理完成！
✅ PHRS充值监听服务启动成功
🔄 启动轮询，间隔: 10秒
🔍 执行轮询检查... (15:30:45)
📊 当前区块: 13824610, 最后处理区块: 13824605
```

### 状态监控

- **实时进度**：显示当前处理的区块范围
- **处理统计**：显示找到和处理的事件数量
- **错误报告**：记录处理失败的交易
- **性能指标**：显示处理速度和完成时间

## 🎯 最佳实践

1. **首次部署**：设置合理的历史起始区块号
2. **定期备份**：备份充值记录数据库
3. **监控日志**：关注错误和异常情况
4. **性能优化**：根据网络情况调整批次大小
5. **测试验证**：在生产环境前充分测试

通过这些功能，您可以确保PHRS监控服务能够完整地处理所有历史和实时的充值交易！
