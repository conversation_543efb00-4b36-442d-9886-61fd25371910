# PHRS 价格系统实现文档

## 概述

本文档描述了为 IapProduct 表添加 `pricePhrs` 字段以及实现 PHRS 兑换 USD 汇率更新系统的完整实现。

## 功能特性

### 1. 数据库扩展
- 为 `iap_products` 表添加了 `pricePhrs` 字段（DECIMAL(20,4)）
- 支持大数值存储，精确到小数点后4位
- 通过数据库迁移文件安全添加字段

### 2. 汇率配置
- **固定汇率**: 1 PHRS = 0.0001 USD（可通过环境变量配置）
- **环境变量**: `PHRS_TO_USD_RATE=0.0001`
- **扩展性**: 预留接口支持未来从外部API获取实时汇率

### 3. 自动价格更新
- **定时任务**: 每小时自动更新所有产品的PHRS价格
- **CRON配置**: 可通过 `PHRS_PRICE_UPDATE_SCHEDULE` 环境变量自定义
- **队列处理**: 使用BullMQ队列系统处理价格更新任务

### 4. 管理API接口
提供完整的管理接口用于PHRS价格管理：

#### 4.1 获取汇率信息
```http
GET /api/admin/phrs-price/rate
```

#### 4.2 获取所有产品价格概览
```http
GET /api/admin/phrs-price/products
```

#### 4.3 手动触发价格更新
```http
POST /api/admin/phrs-price/update
```

#### 4.4 同步更新所有产品价格
```http
POST /api/admin/phrs-price/sync-update
```

#### 4.5 获取单个产品价格信息
```http
GET /api/admin/phrs-price/product/:productId
```

#### 4.6 更新单个产品PHRS价格
```http
POST /api/admin/phrs-price/product/:productId/update
```

#### 4.7 验证PHRS支付金额
```http
POST /api/admin/phrs-price/validate-payment
```

## 技术实现

### 1. 文件结构
```
src/
├── migrations/
│   └── 20250719-add-price-phrs-to-iap-products.js
├── models/
│   └── IapProduct.ts (已更新)
├── services/
│   └── phrsPriceService.ts (新增)
├── jobs/
│   ├── phrsPriceUpdateWorker.ts (新增)
│   ├── schedulePhrsPriceUpdateJob.ts (新增)
│   └── bullmqConfig.ts (已更新)
├── routes/admin/
│   └── phrsPriceRoutes.ts (新增)
└── services/
    └── ServiceManager.ts (已更新)
```

### 2. 核心服务类

#### PhrsPriceService
- `calculatePhrsPrice(usdPrice)`: 根据USD价格计算PHRS价格
- `calculateUsdPrice(phrsPrice)`: 根据PHRS价格计算USD价格
- `updateProductPhrsPrice(productId)`: 更新单个产品PHRS价格
- `updateAllProductsPhrsPrices()`: 批量更新所有产品PHRS价格
- `validatePhrsPayment(productId, phrsAmount)`: 验证PHRS支付金额
- `getCurrentRateInfo()`: 获取当前汇率信息

### 3. 定时任务系统
- **Worker**: `phrsPriceUpdateWorker.ts` 处理价格更新任务
- **Scheduler**: `schedulePhrsPriceUpdateJob.ts` 管理定时任务调度
- **队列**: 使用BullMQ队列系统确保任务可靠执行

## 配置说明

### 环境变量
```bash
# PHRS兑换USD汇率（默认：1 PHRS = 0.0001 USD）
PHRS_TO_USD_RATE=0.0001

# PHRS价格更新定时任务CRON表达式（默认每小时执行一次）
PHRS_PRICE_UPDATE_SCHEDULE=0 0 * * * *
```

### 数据库迁移
```bash
# 执行迁移添加pricePhrs字段
npx sequelize-cli db:migrate
```

## 使用示例

### 1. 价格计算示例
```typescript
import { PhrsPriceService } from '../services/phrsPriceService';

// 计算PHRS价格
const phrsPrice = PhrsPriceService.calculatePhrsPrice(1.99); // 19900 PHRS

// 验证支付金额
const validation = await PhrsPriceService.validatePhrsPayment(1, 9900);
console.log(validation.isValid); // true/false
```

### 2. API调用示例
```bash
# 获取汇率信息
curl -X GET "http://localhost:3457/api/admin/phrs-price/rate"

# 验证支付金额
curl -X POST "http://localhost:3457/api/admin/phrs-price/validate-payment" \
  -H "Content-Type: application/json" \
  -d '{"productId": 1, "phrsAmount": 9900}'

# 手动触发价格更新
curl -X POST "http://localhost:3457/api/admin/phrs-price/update"
```

## 测试

### 1. 单元测试
运行测试脚本验证功能：
```bash
npx ts-node scripts/test-phrs-price-simple.ts
```

### 2. 创建测试数据
```bash
npx ts-node scripts/create-test-iap-products.ts
```

## 价格计算逻辑

### 基本公式
- **PHRS价格 = USD价格 ÷ PHRS汇率**
- **USD价格 = PHRS价格 × PHRS汇率**

### 示例计算
- 产品价格: $1.99 USD
- PHRS汇率: 1 PHRS = 0.0001 USD
- PHRS价格: 1.99 ÷ 0.0001 = 19,900 PHRS

### 精度处理
- 所有价格计算保留4位小数
- 支付验证允许0.0001 PHRS的容差

## 监控和日志

### 1. 任务执行日志
- 价格更新任务执行状态
- 更新的产品数量和详情
- 错误处理和重试机制

### 2. API访问日志
- 管理接口调用记录
- 支付验证结果
- 错误响应和异常处理

## 安全考虑

### 1. 管理接口保护
- 建议添加管理员权限验证中间件
- 限制API访问频率
- 记录敏感操作日志

### 2. 数据完整性
- 价格更新事务处理
- 支付金额验证容差控制
- 异常情况回滚机制

## 扩展计划

### 1. 实时汇率支持
- 集成外部汇率API
- 汇率变化通知机制
- 历史汇率记录

### 2. 批量操作优化
- 数据库批量更新优化
- 缓存机制引入
- 性能监控和优化

### 3. 多币种支持
- 扩展支持更多加密货币
- 统一汇率管理接口
- 跨币种价格转换

## 总结

PHRS价格系统已成功实现并集成到现有的IAP系统中。系统提供了完整的价格管理功能，包括自动更新、手动管理、支付验证等核心功能。通过合理的架构设计和完善的测试，确保了系统的稳定性和可扩展性。
