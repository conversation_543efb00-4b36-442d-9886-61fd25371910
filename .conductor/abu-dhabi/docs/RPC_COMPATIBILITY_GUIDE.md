# RPC兼容性问题解决指南

## 问题描述

在使用某些RPC提供商时，可能会遇到以下错误：

```
Error: could not coalesce error (error={ "code": -32601, "data": "", "message": "METHOD_NOT_FOUND" }, payload={ "id": 4, "jsonrpc": "2.0", "method": "eth_newFilter", "params": [ { "address": [ "0x..." ], "topics": [ "0x..." ] } ] }, code=UNKNOWN_ERROR, version=6.14.0)
```

## 错误原因

- **错误代码**: `-32601` (METHOD_NOT_FOUND)
- **失败的方法**: `eth_newFilter`
- **根本原因**: 不是所有的RPC提供商都支持 `eth_newFilter` 方法

`eth_newFilter` 方法用于创建事件过滤器来实时监听区块链事件，但一些RPC提供商（特别是第三方服务）为了性能和成本考虑，可能不支持这个方法。

## 解决方案

我们已经将事件监听从实时过滤器模式改为轮询模式，以提高RPC兼容性。

### 修改内容

1. **移除实时事件监听**:
   ```typescript
   // 之前的代码
   this.contract.on('Deposit', this.handleDepositEvent.bind(this));
   ```

2. **改为轮询模式**:
   ```typescript
   // 新的代码
   this.startPolling();
   ```

### 轮询机制

- **轮询间隔**: 10秒
- **工作原理**: 定期查询新区块中的事件
- **优势**: 兼容所有RPC提供商
- **劣势**: 可能有轻微的延迟（最多10秒）

## 配置建议

### 推荐的RPC提供商

1. **官方RPC** (最佳选择):
   ```
   PHAROS_RPC_URL=https://rpc.pharos.network
   ```

2. **测试网RPC**:
   ```
   PHAROS_RPC_URL=https://testnet-rpc.pharos.network
   ```

3. **第三方RPC** (如果官方不可用):
   ```
   PHAROS_RPC_URL=https://api.zan.top/node/v1/pharos/testnet/YOUR_API_KEY
   ```

### 环境变量配置

在 `.env` 文件中设置：

```bash
# Pharos网络RPC URL
PHAROS_RPC_URL=https://rpc.pharos.network

# 或者使用测试网
PHAROS_RPC_URL=https://testnet-rpc.pharos.network

# 轮询间隔（可选，默认10秒）
PHRS_POLLING_INTERVAL=10000
```

## 性能优化

### 轮询间隔调整

可以根据需要调整轮询间隔：

```typescript
// 在 phrsDepositService.ts 中
const pollInterval = parseInt(process.env.PHRS_POLLING_INTERVAL || '10000');
```

- **更短间隔** (5秒): 更快的事件检测，但增加RPC调用
- **更长间隔** (30秒): 减少RPC调用，但事件检测延迟更长

### 批量处理

轮询模式的优势是可以批量处理多个事件：

```typescript
// 一次查询可以获取多个区块的所有事件
const events = await this.contract.queryFilter(
  filter,
  this.lastProcessedBlock + 1,
  currentBlock
);
```

## 监控和日志

### 日志输出

轮询模式会输出以下日志：

```
开始监听PHRS充值事件（轮询模式）...
检查区块 12345 到 12350，发现 2 个充值事件
收到PHRS充值事件: { user: '0x...', amount: '1.0', ... }
```

### 错误处理

如果轮询过程中出现错误，系统会：

1. 记录错误日志
2. 继续下一次轮询
3. 不会停止整个监听服务

## 故障排除

### 常见问题

1. **RPC连接超时**:
   ```
   解决方案: 检查网络连接，尝试其他RPC端点
   ```

2. **区块查询失败**:
   ```
   解决方案: 增加重试机制，降低查询频率
   ```

3. **事件解析错误**:
   ```
   解决方案: 检查合约ABI是否正确
   ```

### 调试模式

启用详细日志：

```bash
LOG_LEVEL=debug npm run dev
```

### 健康检查

检查PHRS充值服务状态：

```bash
curl http://localhost:3456/api/phrs-deposit/health
```

## 最佳实践

1. **使用官方RPC**: 优先使用官方提供的RPC端点
2. **设置合理的轮询间隔**: 平衡实时性和性能
3. **监控RPC调用**: 避免超出API限制
4. **实现重试机制**: 处理临时网络问题
5. **记录详细日志**: 便于问题排查

## 未来改进

1. **自适应轮询**: 根据网络活动调整轮询频率
2. **多RPC支持**: 自动切换到备用RPC端点
3. **WebSocket支持**: 如果RPC支持，优先使用WebSocket
4. **事件缓存**: 避免重复处理相同事件

## 总结

通过将事件监听从实时过滤器模式改为轮询模式，我们解决了RPC兼容性问题，确保系统可以在各种RPC提供商上稳定运行。虽然可能有轻微的延迟，但这是一个可接受的权衡，特别是考虑到系统的稳定性和兼容性。
