# 牧场区列表获取 API 文档

本文档描述了获取用户牧场区列表的API接口。通过该接口，客户端可以获取用户所有牧场区的详细信息，包括解锁状态、等级、牛舍数量、产量等数据。

## 基本信息

- 基础路径: `/api/farm/farm-plots`
- 所有接口都需要用户身份验证（钱包认证）

## API 接口

### 获取用户的牧场区列表

获取当前用户拥有的所有牧场区信息，包括已解锁和未解锁的牧场区。

**请求方法**: GET

**路径**: `/api/farm/farm-plots`

**请求参数**: 无需额外参数

**认证要求**: 需要钱包认证

**响应字段说明**:

| 字段名 | 类型 | 描述 |
| --- | --- | --- |
| id | Number | 牧场区记录ID |
| walletId | Number | 用户钱包ID |
| plotNumber | Number | 牧场区编号（1-20） |
| level | Number | 牧场区等级（1-20） |
| barnCount | Number | 牛舍数量 |
| milkProduction | Number | 每次产出获得的牛奶量（3位小数精度） |
| productionSpeed | Number | 每次产出所需时间（秒，3位小数精度） |
| unlockCost | Number | 解锁费用 |
| upgradeCost | Number | 升级费用 |
| lastProductionTime | Date | 上次产出时间 |
| isUnlocked | Boolean | 是否已解锁 |
| accumulatedMilk | Number | 累积的牛奶量 |
| createdAt | Date | 记录创建时间 |
| updatedAt | Date | 记录更新时间 |
| hasBoost | Boolean | 是否有加成效果（仅已解锁农场区） |
| boostMultiplier | Number | 加成倍数（仅已解锁农场区） |
| nextUpgradeGrowth | Object/null | 下次升级预览数据（仅已解锁农场区，未解锁为null） |

**nextUpgradeGrowth 字段说明**:

当农场区已解锁时，`nextUpgradeGrowth` 包含下次升级后的预览数据：

| 字段名 | 类型 | 描述 |
| --- | --- | --- |
| nextProductionSpeed | Number | 下次升级后的生产速度（3位小数精度） |
| nextBarnCount | Number | 下次升级后的牛舍数量 |
| nextMilkProduction | Number | 下次升级后的产量（3位小数精度） |

**升级公式说明**:

- **产量计算**: 1 × (1.5)^(等级-1)
- **生产速度**: 每次升级提升5%，新速度 = 当前速度 ÷ 1.05
- **升级费用**: 每次升级提升1.5倍，新费用 = 当前费用 × 1.5
- **解锁费用**: 编号1免费，编号2开始 = 2000 × 2^(编号-2)
- **牛舍数量**: 每次升级 +1个

**注意事项**:

1. 接口会自动初始化新用户的牧场区（如果用户没有牧场区记录）
2. 对于未解锁的牧场区：
   - `nextUpgradeGrowth` 为 `null`
   - `barnCount` 为 `0`
   - `milkProduction` 为 `0`
   - `hasBoost` 和 `boostMultiplier` 字段不存在
3. 所有数值使用BigNumber.js计算，确保3位小数精度
4. 已解锁的农场区包含完整的升级预览数据

**响应示例**:

```json
{
	"ok": true,
	"data": {
		"farmPlots": [
			{
				"id": 1,
				"walletId": 1,
				"plotNumber": 1,
				"level": 2,
				"barnCount": 2,
				"milkProduction": 1.5,
				"productionSpeed": 4.762,
				"unlockCost": 0,
				"upgradeCost": 300,
				"lastProductionTime": "2025-06-18T03:58:09.880Z",
				"isUnlocked": true,
				"accumulatedMilk": 0,
				"createdAt": "2025-06-18 07:17:29",
				"updatedAt": "2025-06-18T03:58:09.881Z",
				"hasBoost": false,
				"boostMultiplier": 1,
				"nextUpgradeGrowth": {
					"nextProductionSpeed": 4.535,
					"nextBarnCount": 3,
					"nextMilkProduction": 2.25
				}
			},
			{
				"id": 2,
				"walletId": 1,
				"plotNumber": 2,
				"level": 0,
				"barnCount": 0,
				"milkProduction": 0,
				"productionSpeed": 0,
				"unlockCost": 200,
				"upgradeCost": 0,
				"lastProductionTime": "2025-06-18 07:17:29",
				"isUnlocked": false,
				"accumulatedMilk": 0,
				"createdAt": "2025-06-18 07:17:29",
				"updatedAt": "2025-06-18 07:17:29",
				"nextUpgradeGrowth": null
			},
			{
				"id": 63,
				"walletId": 24,
				"plotNumber": 3,
				"level": 0,
				"barnCount": 0,
				"milkProduction": 0,
				"productionSpeed": 0,
				"unlockCost": 400,
				"upgradeCost": 0,
				"lastProductionTime": "2025-05-14 16:18:42",
				"isUnlocked": false,
				"accumulatedMilk": 0,
				"createdAt": "2025-05-14 16:18:42",
				"updatedAt": "2025-05-14 16:18:42"
			},
			{
				"id": 64,
				"walletId": 24,
				"plotNumber": 4,
				"level": 0,
				"barnCount": 0,
				"milkProduction": 0,
				"productionSpeed": 0,
				"unlockCost": 800,
				"upgradeCost": 0,
				"lastProductionTime": "2025-05-14 16:18:42",
				"isUnlocked": false,
				"accumulatedMilk": 0,
				"createdAt": "2025-05-14 16:18:42",
				"updatedAt": "2025-05-14 16:18:42"
			},
			{
				"id": 65,
				"walletId": 24,
				"plotNumber": 5,
				"level": 0,
				"barnCount": 0,
				"milkProduction": 0,
				"productionSpeed": 0,
				"unlockCost": 1600,
				"upgradeCost": 0,
				"lastProductionTime": "2025-05-14 16:18:42",
				"isUnlocked": false,
				"accumulatedMilk": 0,
				"createdAt": "2025-05-14 16:18:42",
				"updatedAt": "2025-05-14 16:18:42"
			},
			{
				"id": 66,
				"walletId": 24,
				"plotNumber": 6,
				"level": 0,
				"barnCount": 0,
				"milkProduction": 0,
				"productionSpeed": 0,
				"unlockCost": 3200,
				"upgradeCost": 0,
				"lastProductionTime": "2025-05-14 16:18:42",
				"isUnlocked": false,
				"accumulatedMilk": 0,
				"createdAt": "2025-05-14 16:18:42",
				"updatedAt": "2025-05-14 16:18:42"
			},
			{
				"id": 67,
				"walletId": 24,
				"plotNumber": 7,
				"level": 0,
				"barnCount": 0,
				"milkProduction": 0,
				"productionSpeed": 0,
				"unlockCost": 6400,
				"upgradeCost": 0,
				"lastProductionTime": "2025-05-14 16:18:42",
				"isUnlocked": false,
				"accumulatedMilk": 0,
				"createdAt": "2025-05-14 16:18:42",
				"updatedAt": "2025-05-14 16:18:42"
			},
			{
				"id": 68,
				"walletId": 24,
				"plotNumber": 8,
				"level": 0,
				"barnCount": 0,
				"milkProduction": 0,
				"productionSpeed": 0,
				"unlockCost": 12800,
				"upgradeCost": 0,
				"lastProductionTime": "2025-05-14 16:18:42",
				"isUnlocked": false,
				"accumulatedMilk": 0,
				"createdAt": "2025-05-14 16:18:42",
				"updatedAt": "2025-05-14 16:18:42"
			},
			{
				"id": 69,
				"walletId": 24,
				"plotNumber": 9,
				"level": 0,
				"barnCount": 0,
				"milkProduction": 0,
				"productionSpeed": 0,
				"unlockCost": 25600,
				"upgradeCost": 0,
				"lastProductionTime": "2025-05-14 16:18:42",
				"isUnlocked": false,
				"accumulatedMilk": 0,
				"createdAt": "2025-05-14 16:18:42",
				"updatedAt": "2025-05-14 16:18:42"
			},
			{
				"id": 70,
				"walletId": 24,
				"plotNumber": 10,
				"level": 0,
				"barnCount": 0,
				"milkProduction": 0,
				"productionSpeed": 0,
				"unlockCost": 51200,
				"upgradeCost": 0,
				"lastProductionTime": "2025-05-14 16:18:42",
				"isUnlocked": false,
				"accumulatedMilk": 0,
				"createdAt": "2025-05-14 16:18:42",
				"updatedAt": "2025-05-14 16:18:42"
			},
			{
				"id": 71,
				"walletId": 24,
				"plotNumber": 11,
				"level": 0,
				"barnCount": 0,
				"milkProduction": 0,
				"productionSpeed": 0,
				"unlockCost": 102400,
				"upgradeCost": 0,
				"lastProductionTime": "2025-05-14 16:18:42",
				"isUnlocked": false,
				"accumulatedMilk": 0,
				"createdAt": "2025-05-14 16:18:42",
				"updatedAt": "2025-05-14 16:18:42"
			},
			{
				"id": 72,
				"walletId": 24,
				"plotNumber": 12,
				"level": 0,
				"barnCount": 0,
				"milkProduction": 0,
				"productionSpeed": 0,
				"unlockCost": 204800,
				"upgradeCost": 0,
				"lastProductionTime": "2025-05-14 16:18:42",
				"isUnlocked": false,
				"accumulatedMilk": 0,
				"createdAt": "2025-05-14 16:18:42",
				"updatedAt": "2025-05-14 16:18:42"
			},
			{
				"id": 73,
				"walletId": 24,
				"plotNumber": 13,
				"level": 0,
				"barnCount": 0,
				"milkProduction": 0,
				"productionSpeed": 0,
				"unlockCost": 409600,
				"upgradeCost": 0,
				"lastProductionTime": "2025-05-14 16:18:42",
				"isUnlocked": false,
				"accumulatedMilk": 0,
				"createdAt": "2025-05-14 16:18:42",
				"updatedAt": "2025-05-14 16:18:42"
			},
			{
				"id": 74,
				"walletId": 24,
				"plotNumber": 14,
				"level": 0,
				"barnCount": 0,
				"milkProduction": 0,
				"productionSpeed": 0,
				"unlockCost": 819200,
				"upgradeCost": 0,
				"lastProductionTime": "2025-05-14 16:18:42",
				"isUnlocked": false,
				"accumulatedMilk": 0,
				"createdAt": "2025-05-14 16:18:42",
				"updatedAt": "2025-05-14 16:18:42"
			},
			{
				"id": 75,
				"walletId": 24,
				"plotNumber": 15,
				"level": 0,
				"barnCount": 0,
				"milkProduction": 0,
				"productionSpeed": 0,
				"unlockCost": 1638400,
				"upgradeCost": 0,
				"lastProductionTime": "2025-05-14 16:18:42",
				"isUnlocked": false,
				"accumulatedMilk": 0,
				"createdAt": "2025-05-14 16:18:42",
				"updatedAt": "2025-05-14 16:18:42"
			},
			{
				"id": 76,
				"walletId": 24,
				"plotNumber": 16,
				"level": 0,
				"barnCount": 0,
				"milkProduction": 0,
				"productionSpeed": 0,
				"unlockCost": 3276800,
				"upgradeCost": 0,
				"lastProductionTime": "2025-05-14 16:18:42",
				"isUnlocked": false,
				"accumulatedMilk": 0,
				"createdAt": "2025-05-14 16:18:42",
				"updatedAt": "2025-05-14 16:18:42"
			},
			{
				"id": 77,
				"walletId": 24,
				"plotNumber": 17,
				"level": 0,
				"barnCount": 0,
				"milkProduction": 0,
				"productionSpeed": 0,
				"unlockCost": 6553600,
				"upgradeCost": 0,
				"lastProductionTime": "2025-05-14 16:18:42",
				"isUnlocked": false,
				"accumulatedMilk": 0,
				"createdAt": "2025-05-14 16:18:42",
				"updatedAt": "2025-05-14 16:18:42"
			},
			{
				"id": 78,
				"walletId": 24,
				"plotNumber": 18,
				"level": 0,
				"barnCount": 0,
				"milkProduction": 0,
				"productionSpeed": 0,
				"unlockCost": 13107200,
				"upgradeCost": 0,
				"lastProductionTime": "2025-05-14 16:18:42",
				"isUnlocked": false,
				"accumulatedMilk": 0,
				"createdAt": "2025-05-14 16:18:42",
				"updatedAt": "2025-05-14 16:18:42"
			},
			{
				"id": 79,
				"walletId": 24,
				"plotNumber": 19,
				"level": 0,
				"barnCount": 0,
				"milkProduction": 0,
				"productionSpeed": 0,
				"unlockCost": 26214400,
				"upgradeCost": 0,
				"lastProductionTime": "2025-05-14 16:18:42",
				"isUnlocked": false,
				"accumulatedMilk": 0,
				"createdAt": "2025-05-14 16:18:42",
				"updatedAt": "2025-05-14 16:18:42"
			},
			{
				"id": 80,
				"walletId": 24,
				"plotNumber": 20,
				"level": 0,
				"barnCount": 0,
				"milkProduction": 0,
				"productionSpeed": 0,
				"unlockCost": 52428800,
				"upgradeCost": 0,
				"lastProductionTime": "2025-05-14 16:18:42",
				"isUnlocked": false,
				"accumulatedMilk": 0,
				"createdAt": "2025-05-14 16:18:42",
				"updatedAt": "2025-05-14 16:18:42"
			}
		]
	}
}
```

**解锁费用参考表**:

| 农场区编号 | 解锁费用 (GEM) |
| --- | --- |
| 1 | 0 (免费) |
| 2 | 2,000 |
| 3 | 4,000 |
| 4 | 8,000 |
| 5 | 16,000 |
| 6 | 32,000 |
| 7 | 64,000 |
| 8 | 128,000 |
| 9 | 256,000 |
| 10 | 512,000 |
| 11 | 1,024,000 |
| 12 | 2,048,000 |
| 13 | 4,096,000 |
| 14 | 8,192,000 |
| 15 | 16,384,000 |
| 16 | 32,768,000 |
| 17 | 65,536,000 |
| 18 | 131,072,000 |
| 19 | 262,144,000 |
| 20 | 524,288,000 |