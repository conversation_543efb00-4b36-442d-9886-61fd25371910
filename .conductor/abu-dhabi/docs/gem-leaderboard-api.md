# Gem排行榜 API
### 获取Gem排行榜数据

**接口路径**：`GET /api/gem-leaderboard`

**接口描述**：获取基于gem数量的用户排行榜数据，包括排名、用户信息和gem数量，同时返回当前用户的排名信息。

**请求方法**：GET

**认证要求**：需要钱包认证（walletAuthMiddleware）

**请求参数**：

| 参数名 | 类型 | 必填 | 描述 |
|--|--|--|--|
| limit | Number | 否 | 限制返回的记录数量，默认值为100 |
| offset | Number | 否 | 分页偏移量，默认值为0 |

**响应结果**：

```json
{
  "ok": true,
  "data": {
    "leaderboard": [
      {
        "userId": 123,
        "walletId": 456,
        "walletAddress": "0x1234567890abcdef",
        "username": "用户名",
        "photoUrl": "https://example.com/photo.jpg",
        "gemAmount": 1000,
        "rank": 1
      },
      {
        "userId": 124,
        "walletId": 457,
        "walletAddress": "0xabcdef1234567890",
        "username": "用户名2",
        "photoUrl": "https://example.com/photo2.jpg",
        "gemAmount": 900,
        "rank": 2
      }
      // 更多排行榜数据...
    ],
    "pagination": {
      "total": 500,
      "limit": 100,
      "offset": 0,
      "hasMore": true
    },
    "userInfo": {
      "rank": 15,
      "gemAmount": 500
    }
  }
}
```

**响应字段说明**：

| 字段名 | 类型 | 描述 |
|--|--|--|
| leaderboard | Array | 排行榜数据数组 |
| leaderboard[].userId | Number | 用户ID |
| leaderboard[].walletId | Number | 钱包ID |
| leaderboard[].walletAddress | String | 钱包地址 |
| leaderboard[].username | String | 用户名 |
| leaderboard[].photoUrl | String | 用户头像URL |
| leaderboard[].gemAmount | Number | 用户拥有的gem数量 |
| leaderboard[].rank | Number | 用户在排行榜中的排名 |
| pagination | Object | 分页信息 |
| pagination.total | Number | 总记录数 |
| pagination.limit | Number | 每页记录数 |
| pagination.offset | Number | 当前偏移量 |
| pagination.hasMore | Boolean | 是否还有更多数据 |
| userInfo | Object | 当前用户信息 |
| userInfo.rank | Number | 当前用户的排名 |
| userInfo.gemAmount | Number | 当前用户的gem数量 |

**错误码**：

| 状态码 | 错误信息 | 描述 |
|--|--|--|
| 400 | 参数验证失败 | 当请求参数格式不正确时返回 |
| 400 | 无效的limit参数 | 当limit参数小于等于0或不是数字时返回 |
| 400 | 无效的offset参数 | 当offset参数小于0或不是数字时返回 |
| 401 | 未授权 | 当用户未登录或token无效时返回 |
| 500 | 获取Gem排行榜失败 | 当服务器内部错误导致无法获取排行榜数据时返回 |

**业务逻辑说明**：
1. 排行榜按照用户gem数量降序排列
2. 只有gem数量大于0的用户才会出现在排行榜中
3. 分页参数limit和offset用于控制返回的数据量，适合移动端分页加载
4. 返回的userInfo包含当前登录用户的排名和gem数量，方便前端展示用户在排行榜中的位置
5. 排名从1开始，数值越小表示排名越高