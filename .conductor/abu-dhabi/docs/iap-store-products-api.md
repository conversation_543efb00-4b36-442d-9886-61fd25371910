# IAP 商店商品列表 API

## 接口信息

- **路径**: `/api/iap/store/products`
- **方法**: GET
- **描述**: 获取IAP商店中所有可购买的商品列表，包含购买限制检查和时间跳跃收益预览
- **认证**: 需要钱包认证，请求头中必须包含 `Authorization: Bearer <token>`

## 请求参数

### 查询参数

无需查询参数

## 响应参数

### 成功响应

**状态码**: 200

```json
{
  "ok": true,
  "products": [
    {
      "id": 1,
      "productId": "speed_boost_2x_24h",
      "name": "2倍速度提升道具",
      "type": "speed_boost",
      "multiplier": 2,
      "duration": 24,
      "quantity": 1,
      "priceUsd": 4.99,
      "priceKaia": 10.5,
      "dailyLimit": 0,
      "accountLimit": 0,
      "description": "提升生产速度2倍，持续24小时",
      "isActive": true,
      "canPurchase": true,
      "reason": "",
      "createdAt": "2024-01-15T10:30:00.000Z",
      "updatedAt": "2024-01-15T10:30:00.000Z"
    },
    {
      "id": 2,
      "productId": "time_warp_4h",
      "name": "4小时时间跳跃",
      "type": "time_warp",
      "multiplier": 1,
      "duration": 4,
      "quantity": 1,
      "priceUsd": 2.99,
      "priceKaia": 6.0,
      "dailyLimit": 0,
      "accountLimit": 0,
      "description": "立即获得4小时的产出",
      "isActive": true,
      "canPurchase": true,
      "reason": "",
      "expectedRewards": {
        "gemsEarned": 57600.000,
        "milkProduced": 36000.000,
        "milkProcessed": 28800.000,
        "farmProductionPerSecond": 2.500,
        "deliveryProcessingPerSecond": 2.000,
        "hasVip": false,
        "hasSpeedBoost": false,
        "speedBoostMultiplier": 1.00
      },
      "createdAt": "2024-01-15T10:30:00.000Z",
      "updatedAt": "2024-01-15T10:30:00.000Z"
    },
    {
      "id": 3,
      "productId": "vip_membership_30d",
      "name": "VIP会员30天",
      "type": "vip_membership",
      "multiplier": null,
      "duration": null,
      "quantity": null,
      "priceUsd": 19.99,
      "priceKaia": 42.0,
      "dailyLimit": 1,
      "accountLimit": 0,
      "description": "享受30天VIP特权，包含多项加成",
      "isActive": true,
      "canPurchase": true,
      "reason": "",
      "config": {
        "durationDays": 30,
        "deliverySpeedBonus": 0.3,
        "blockPriceBonus": 0.2,
        "productionSpeedBonus": 0.3,
        "benefits": ["delivery_speed_boost", "block_price_boost", "production_speed_boost"]
      },
      "createdAt": "2024-01-15T10:30:00.000Z",
      "updatedAt": "2024-01-15T10:30:00.000Z"
    }
  ]
}
```

## 字段说明

### products 数组字段

| 字段名 | 类型 | 描述 |
| ------ | ---- | ---- |
| id | number | 商品ID |
| productId | string | 商品唯一标识 |
| name | string | 商品名称 |
| type | string | 商品类型，可能值: `speed_boost`(速度提升), `time_warp`(时间跳跃), `vip_membership`(VIP会员), `special_offer`(特殊套餐) |
| multiplier | number\|null | 加成倍数（对于速度提升道具） |
| duration | number\|null | 持续时间（小时） |
| quantity | number\|null | 数量 |
| priceUsd | number | 美元价格 |
| priceKaia | number | KAIA代币价格 |
| dailyLimit | number | 每日购买限制（0表示无限制） |
| accountLimit | number | 账号总购买限制（0表示无限制） |
| description | string | 商品描述（支持多语言） |
| isActive | boolean | 是否激活 |
| canPurchase | boolean | 当前是否可以购买 |
| reason | string | 不能购买的原因（当canPurchase为false时） |
| config | object\|null | 商品配置信息（对于VIP会员和特殊套餐） |
| expectedRewards | object\|null | 预期收益信息（仅对时间跳跃商品） |
| createdAt | string | 创建时间 |
| updatedAt | string | 更新时间 |

### expectedRewards 字段说明（仅时间跳跃商品）

| 字段名 | 类型 | 描述 |
| ------ | ---- | ---- |
| gemsEarned | number | 预期获得的GEM数量 |
| milkProduced | number | 预期生产的牛奶数量 |
| milkProcessed | number | 预期处理的牛奶数量 |
| farmProductionPerSecond | number | 当前农场每秒产量 |
| deliveryProcessingPerSecond | number | 当前出货线每秒处理量 |
| hasVip | boolean | 是否有VIP加成 |
| hasSpeedBoost | boolean | 是否有速度加成 |
| speedBoostMultiplier | number | 速度加成倍数 |

## 业务逻辑说明

### 购买限制检查

1. **每日类型限制**: 对于`speed_boost`和`time_warp`类型的商品，每日只能购买1次同类型商品
2. **每日商品限制**: 其他商品按照`dailyLimit`字段进行每日购买限制
3. **账号限制**: 按照`accountLimit`字段检查账号总购买次数
4. **VIP会员检查**: 如果已有激活的VIP会员，则不能再购买VIP会员商品

### 商品排序

商品按照以下规则排序：
1. 首先按商品类型（type）升序排列
2. 然后按美元价格（priceUsd）升序排列

### 时间跳跃收益预览

对于`time_warp`类型的商品，系统会自动计算并返回`expectedRewards`字段，包含：

1. **收益计算**: 根据用户当前的农场区块和出货线状态计算预期收益
2. **VIP加成**: 自动考虑用户的VIP状态对收益的影响
   - +30% 出货速度
   - +20% 方块价格
   - +30% 农场生产速度
3. **速度加成**: 自动考虑激活的速度加成道具的影响
4. **实时计算**: 每次请求都会重新计算，确保数据准确性

**计算公式**:
```
时间内生产牛奶 = 农场每秒产量 × 时间（秒）
时间内可处理牛奶 = 出货线每秒处理量 × 时间（秒）
实际处理牛奶 = min(生产牛奶, 可处理牛奶)
获得GEM = 实际处理牛奶 ÷ 方块单位 × 方块价格
```

### 多语言支持

商品描述支持多语言，系统会根据请求的语言设置返回对应的描述文本。

---