# 增加宝石 API

## 接口信息

- **URL**: `/api/wallet/increase-gem`
- **方法**: POST
- **描述**: 增加用户钱包的宝石值
- **认证要求**: 需要钱包认证

## 请求参数

### 请求体 (JSON)

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|------|------|
| milkAmount | Number | 是 | 要增加的宝石数量|

## 响应结果

### 成功响应

- **状态码**: 200 OK
- **响应体**:

```json
{
	"ok": true,
	"data": {
		"gem": 484095,
		"pendingMilk": 50,
		"milkUsed": 10,
		"gemAmount": 10
	},
	"message": "success.milkToGem"
}
```

## 业务逻辑说明

1. 验证请求参数，确保 `amount` 是有效的数字且大于0
2. 暂时没有什么增加限制
3. 返回更新后的宝石总量