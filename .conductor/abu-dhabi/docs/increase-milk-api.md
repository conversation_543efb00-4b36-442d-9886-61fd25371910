# 增加待处理牛奶 API

## 接口信息

- **URL**: `/api/wallet/increase-milk`
- **方法**: POST
- **描述**: 增加用户钱包的待处理牛奶数量
- **认证要求**: 需要钱包认证

## 请求参数

### 请求体 (JSON)

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|------|------|
| pendingMilk | Number | 是 | 要增加的待处理牛奶数量，必须是大于等于0的数字 |

## 响应结果

### 成功响应

- **状态码**: 200 OK
- **响应体**:

```json
{
  "ok": true,
  "data": {
    "pendingMilk": 100 // 更新后的待处理牛奶总量
  },
  "message": "成功增加待处理牛奶"
}
```

## 业务逻辑说明

1. 验证请求参数，确保 `pendingMilk` 是有效的数字且大于等于0
2. 暂时没有什么增加限制
