# 新任务系统用户任务列表 API 文档

本文档描述了新任务系统中获取用户任务列表的API接口。通过该接口，客户端可以获取用户的任务状态、进度信息以及任务配置详情。

## 基本信息

- **接口路径**: `/api/new-tasks/user`
- **请求方法**: GET
- **认证要求**: 需要钱包认证（walletAuthMiddleware）
- **语言支持**: 支持国际化（languageMiddleware）

## API 接口详情

### 获取用户任务列表

获取当前用户的任务列表，包括任务状态、进度信息和奖励详情。

**请求方法**: GET

**路径**: `/api/new-tasks/user`

**查询参数**:

| 参数名 | 类型 | 必填 | 默认值 | 描述 |
|--------|------|------|--------|------|
| main | boolean | 否 | false | 是否为主界面请求，为true时只返回优先级最高的单个任务 |
| showAll | boolean | 否 | false | 是否显示所有任务（包括未接取的任务） |

**认证要求**: 
- 需要在请求头中包含有效的JWT token
- 通过 `Authorization: Bearer <token>` 方式传递

**响应格式**:

```json
{
  "ok": true,
  "data": {
    "tasks": [],
    "total": 0,
    "mainTask": null
  },
  "message": "获取任务列表成功"
}
```

## 响应字段说明

### 主要响应结构

| 字段名 | 类型 | 描述 |
|--------|------|------|
| ok | boolean | 请求是否成功 |
| data | object | 响应数据 |
| message | string | 响应消息 |

### data 对象字段

| 字段名 | 类型 | 描述 |
|--------|------|------|
| tasks | array | 任务列表（当main=false时返回） |
| total | number | 任务总数 |
| mainTask | object/null | 主界面任务（当main=true时返回） |

### 任务对象字段

每个任务对象包含以下字段：

#### 任务状态信息

| 字段名 | 类型 | 描述 |
|--------|------|------|
| id | number | 用户任务状态记录ID |
| taskId | number | 任务配置ID |
| status | string | 任务状态：not_accepted/accepted/completed/claimed |
| statusDescription | string | 状态描述：未接取/进行中/已完成/已领取 |
| currentProgress | number | 当前进度 |
| targetProgress | number | 目标进度 |
| progressText | string | 进度文本（如：5/10） |
| progressPercentage | number | 进度百分比（0-100） |
| canClaim | boolean | 是否可以领取奖励 |
| isCompleted | boolean | 是否已完成 |
| isClaimed | boolean | 是否已领取奖励 |
| isInProgress | boolean | 是否正在进行中 |
| acceptedAt | date/null | 接取时间 |
| completedAt | date/null | 完成时间 |
| claimedAt | date/null | 领取时间 |
| displayPriority | number | 显示优先级（数值越小优先级越高） |

#### 任务配置信息

| 字段名 | 类型 | 描述 |
|--------|------|------|
| taskConfig | object | 任务配置详情 |

**taskConfig 对象字段**:

| 字段名 | 类型 | 描述 |
|--------|------|------|
| id | number | 任务配置ID |
| describe | string | 任务描述 |
| type | number | 任务类型（1-解锁区域 2-升级区域 3-升级流水线 4-邀请好友） |
| typeDescription | string | 任务类型描述 |
| parameterDescription | string | 参数描述 |
| rewards | array | 奖励列表 |
| hasRewards | boolean | 是否有奖励 |

**rewards 数组元素**:

| 字段名 | 类型 | 描述 |
|--------|------|------|
| type | string | 奖励类型：diamond/box/coin/item |
| amount | number | 奖励数量 |

## 任务类型说明

| 类型值 | 类型名称 | 描述 |
|--------|----------|------|
| 1 | UNLOCK_AREA | 解锁指定区域 |
| 2 | UPGRADE_FARM | 升级指定牧场区域至XX级 |
| 3 | UPGRADE_DELIVERY | 升级流水线至XX级 |
| 4 | INVITE_FRIENDS | 邀请好友 |

## 任务状态说明

| 状态值 | 状态描述 | 说明 |
|--------|----------|------|
| not_accepted | 未接取 | 任务尚未被用户接取 |
| accepted | 进行中 | 任务已接取，正在进行中 |
| completed | 已完成 | 任务已完成，可以领取奖励 |
| claimed | 已领取 | 任务奖励已被领取 |

## 请求示例

### 获取普通任务列表

```bash
curl -X GET "https://api.example.com/api/new-tasks/user" \
  -H "Authorization: Bearer your-jwt-token" \
  -H "Content-Type: application/json"
```

### 获取主界面任务

```bash
curl -X GET "https://api.example.com/api/new-tasks/user?main=true" \
  -H "Authorization: Bearer your-jwt-token" \
  -H "Content-Type: application/json"
```

### 获取所有任务（包括未接取的）

```bash
curl -X GET "https://api.example.com/api/new-tasks/user?showAll=true" \
  -H "Authorization: Bearer your-jwt-token" \
  -H "Content-Type: application/json"
```

## 响应示例

### 普通任务列表响应

```json
{
  "ok": true,
  "data": {
    "tasks": [
      {
        "id": 1,
        "taskId": 1,
        "status": "completed",
        "statusDescription": "已完成",
        "currentProgress": 1,
        "targetProgress": 1,
        "progressText": "1/1",
        "progressPercentage": 100,
        "canClaim": true,
        "isCompleted": true,
        "isClaimed": false,
        "isInProgress": false,
        "acceptedAt": "2025-01-20T10:00:00.000Z",
        "completedAt": "2025-01-20T10:30:00.000Z",
        "claimedAt": null,
        "displayPriority": 1,
        "taskConfig": {
          "id": 1,
          "describe": "解锁第2个牧场区域",
          "type": 1,
          "typeDescription": "解锁指定区域",
          "parameterDescription": "解锁区域2",
          "rewards": [
            {
              "type": "diamond",
              "amount": 100
            },
            {
              "type": "box",
              "amount": 1
            }
          ],
          "hasRewards": true
        }
      }
    ],
    "total": 1
  },
  "message": "获取任务列表成功"
}
```

### 主界面任务响应

```json
{
  "ok": true,
  "data": {
    "tasks": [],
    "total": 0,
    "mainTask": {
      "id": 2,
      "taskId": 2,
      "status": "accepted",
      "statusDescription": "进行中",
      "currentProgress": 3,
      "targetProgress": 5,
      "progressText": "3/5",
      "progressPercentage": 60,
      "canClaim": false,
      "isCompleted": false,
      "isClaimed": false,
      "isInProgress": true,
      "acceptedAt": "2025-01-20T11:00:00.000Z",
      "completedAt": null,
      "claimedAt": null,
      "displayPriority": 2,
      "taskConfig": {
        "id": 2,
        "describe": "升级牧场区域1至5级",
        "type": 2,
        "typeDescription": "升级指定牧场区域至XX级",
        "parameterDescription": "升级区域1至5级",
        "rewards": [
          {
            "type": "diamond",
            "amount": 200
          }
        ],
        "hasRewards": true
      }
    }
  },
  "message": "获取任务列表成功"
}
```

## 错误响应

### 401 未授权

```json
{
  "ok": false,
  "message": "用户未登录"
}
```

### 500 服务器错误

```json
{
  "ok": false,
  "message": "获取任务列表失败"
}
```

## 业务逻辑说明

1. **自动初始化**: 接口会自动为新用户初始化任务状态
2. **进度更新**: 每次调用接口时会自动更新所有任务的进度
3. **自动接取**: 满足前置条件的任务会被自动接取
4. **任务排序**: 任务按状态优先级排序（已完成 > 进行中 > 其他）
5. **主界面模式**: 当 `main=true` 时，只返回优先级最高的单个任务用于主界面显示

## 相关接口

- [领取任务奖励 API](./new-task-claim-api-update.md)
- [更新任务进度 API](#) - 手动更新任务进度
- [初始化用户任务 API](#) - 初始化用户任务状态

## 注意事项

1. 接口支持国际化，响应消息会根据请求语言返回对应文本
2. 任务进度会在每次请求时自动更新，无需手动调用更新接口
3. 未接取的任务默认不显示，需要设置 `showAll=true` 才能查看
4. 主界面模式下，`tasks` 数组为空，任务信息在 `mainTask` 字段中
5. 所有时间字段均为 ISO 8601 格式的 UTC 时间
