# 推荐状态 API
### 获取用户推荐状态

**接口路径**：`GET /api/referral/status`

**接口描述**：获取用户的推荐状态信息，包括当前推荐人数、各等级状态和今日是否已领取奖励。

**请求方法**：GET

**认证要求**：需要钱包认证（walletAuthMiddleware）

**请求参数**：无需请求参数

**响应结果**：

```json
{
  "ok": true,
  "data": {
    "currentReferrals": 5,
    "status": "可领取",
    "levelsStatus": [
      {
        "requiredReferrals": 5,
        "chestsReward": 1,
        "isAvailable": true,
        "progress": 1.0,
        "name": "每日1个宝箱奖励",
        "status": "可领取"
      },
      {
        "requiredReferrals": 10,
        "chestsReward": 2,
        "isAvailable": false,
        "progress": 0.5,
        "name": "每日2个宝箱奖励",
        "status": "未达成条件"
      },
      {
        "requiredReferrals": 30,
        "chestsReward": 5,
        "isAvailable": false,
        "progress": 0.167,
        "name": "每日5个宝箱奖励",
        "status": "未达成条件"
      },
      {
        "requiredReferrals": 100,
        "chestsReward": 15,
        "isAvailable": false,
        "progress": 0.05,
        "name": "每日15个宝箱奖励",
        "status": "未达成条件"
      }
    ]
  }
}
```

**响应字段说明**：

| 字段名 | 类型 | 描述 |
|--|--|--|
| currentReferrals | Number | 当前推荐人数 |
| status | String | 整体状态，可能值："未达成条件"、"可领取"、"已领取" |
| levelsStatus | Array | 推荐等级状态数组 |
| levelsStatus[].requiredReferrals | Number | 该等级需要的推荐人数 |
| levelsStatus[].chestsReward | Number | 该等级对应的宝箱奖励数量 |
| levelsStatus[].isAvailable | Boolean | 是否已达到该等级 |
| levelsStatus[].progress | Number | 该等级的完成进度(0-1) |
| levelsStatus[].name | String | 该等级的名称 |
| levelsStatus[].status | String | 该等级的状态，可能值："未达成条件"、"可领取"、"已领取" |

**错误码**：

| 状态码 | 错误信息 | 描述 |
|--|--|--|
| 401 | 未授权 | 当用户未登录或token无效时返回 |
| 400 | 用户不存在 | 当用户ID无效时返回 |

**业务逻辑说明**：
1. 统计数据基于用户的推荐关系
2. 今日领取状态基于UserDailyClaim表记录
3. 推荐等级分为4档：5人、10人、30人、100人
4. 每个等级的status字段表示当前状态：
   - 未达成条件：当前推荐人数小于该等级要求
   - 可领取：已达到该等级要求且今日未领取
   - 已领取：已达到该等级要求且今日已领取