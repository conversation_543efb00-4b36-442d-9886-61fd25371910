# 重置一次性4个宝箱领取状态 API

## 接口说明

该接口用于重置用户的一次性4个宝箱领取状态，主要用于测试目的。重置后，用户可以重新领取一次性4个宝箱奖励。

## 接口信息

- **接口路径**: `/api/test-chest/reset-four-chests-collect`
- **请求方法**: POST
- **需要认证**: 是（需要钱包认证中间件）

## 请求头

| 参数名 | 必选 | 类型 | 说明 |
|--------|------|------|------|
| Authorization | 是 | string | 钱包认证token |

## 请求参数

无需请求参数

## 响应参数

### 成功响应

```json
{
  "ok": true,
  "message": "一次性4个宝箱领取状态已重置"
}
```

### 错误响应

```json
{
  "ok": false,
  "message": "重置失败"
}
```

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 400 | 请求参数错误或重置操作失败 |
| 401 | 未授权（缺少token或token无效） |

## 注意事项

1. 该接口仅用于测试环境
2. 调用前需确保已完成钱包认证
3. 重置操作会清除用户的一次性4个宝箱领取状态记录