# 严格批量更新服务配置升级

## 概述

已成功将 `/api/wallet/strict-batch-update-resources` 接口升级为使用新的配置表进行计算和验证，而不是依赖数据库中存储的旧值。

## 修改内容

### 1. 牧场区计算升级

**文件**: `src/services/strictBatchResourceUpdateService.ts`

#### 修改前（使用数据库值）:
```javascript
const baseProductionSpeed = Number(plot.productionSpeed); // 数据库值
const milkProduction = Number(plot.milkProduction);       // 数据库值
const barnCount = Number(plot.barnCount);                 // 数据库值
```

#### 修改后（使用配置表）:
```javascript
// 从farm_configs表获取实际数据
const config = await FarmConfigService.getConfigByGrade(plot.level);
const baseProductionSpeed = 100.0 / config.speed; // speed是百分比，转换为秒
const milkProduction = config.production;          // 每次产量
const barnCount = config.cow;                      // 谷仓数量
```

### 2. 出货线计算升级

#### 修改前（使用数据库值）:
```javascript
const baseDeliverySpeed = Number(deliveryLine.deliverySpeed); // 数据库值
const blockUnit = Number(deliveryLine.blockUnit);             // 数据库值
```

#### 修改后（使用配置表）:
```javascript
// 从delivery_line_configs表获取实际数据
const config = await DeliveryLineConfig.getConfigByGrade(deliveryLine.level);
const baseDeliverySpeed = Number(config.production_interval); // 基础处理间隔（秒）
const blockUnit = Number(config.capacity);                    // 每次处理的牛奶量
```

### 3. GEM转换率计算升级

#### 修改前（使用数据库值）:
```javascript
const milkToGemRate = deliveryLine.blockPrice / deliveryLine.blockUnit;
```

#### 修改后（使用配置表）:
```javascript
const config = await DeliveryLineConfig.getConfigByGrade(deliveryLine.level);
const milkToGemRate = config.profit / config.capacity;
```

## 配置表映射

### farm_configs 表字段
| 配置字段 | 用途 | 说明 |
|----------|------|------|
| `speed` | 生产速度百分比 | 100表示100%（1秒），200表示200%（0.5秒） |
| `production` | 每次产量 | 每个生产周期产出的牛奶量 |
| `cow` | 奶牛数量 | 影响总产量的倍数 |

### delivery_line_configs 表字段
| 配置字段 | 用途 | 说明 |
|----------|------|------|
| `production_interval` | 生产间隔 | 每次处理的时间间隔（秒） |
| `capacity` | 牛奶容量 | 每次处理的牛奶量 |
| `profit` | 牛奶利润 | 每次处理产生的GEM |

## 计算公式

### 牧场区每秒牛奶产量
```javascript
// 基础生产速度 = 100 / speed百分比
const baseProductionSpeed = 100.0 / config.speed;

// 实际生产速度（考虑VIP加成）= 基础速度 / VIP倍率
const actualProductionSpeed = baseProductionSpeed / productionSpeedMultiplier;

// 每秒产量 = (每次产量 × 谷仓数量) / 生产间隔
const plotProductionPerSecond = (config.production * config.cow) / actualProductionSpeed;
```

### 出货线每秒牛奶消耗量
```javascript
// 计算加成效果
const deliverySpeedMultiplier = vipMultiplier * boosterMultiplier;

// 实际处理速度 = 基础速度 / 加成倍率
const actualDeliverySpeed = config.production_interval / deliverySpeedMultiplier;

// 每秒消耗量 = 每次处理量 / 处理间隔
const consumptionPerSecond = config.capacity / actualDeliverySpeed;
```

### GEM转换率
```javascript
// GEM转换率 = 利润 / 容量
const milkToGemRate = config.profit / config.capacity;
```

## 降级方案

为了确保系统稳定性，所有配置获取都有降级方案：

```javascript
try {
  // 尝试从配置表获取数据
  const config = await ConfigService.getConfigByGrade(level);
  if (config) {
    // 使用配置表数据
  } else {
    console.warn('配置不存在，使用数据库值');
    // 使用数据库中的值
  }
} catch (error) {
  console.warn('获取配置失败，使用数据库值:', error);
  // 使用数据库中的值
}
```

## 验证结果

运行 `node scripts/test-strict-batch-update-config.js` 的测试结果：

```
🎉 配置基础计算逻辑验证完成！
✅ 牧场区使用farm_configs表的speed、production、cow字段
✅ 出货线使用delivery_line_configs表的production_interval、capacity、profit字段
✅ VIP和道具加成正确应用
```

### 计算示例
- **牧场区等级1**: 182 × 1 ÷ 0.769 = 236.600 牛奶/秒
- **牧场区等级3**: 276 × 2 ÷ 0.699 = 789.360 牛奶/秒
- **出货线消耗**: 10 ÷ 0.385 = 26.000 牛奶/秒
- **GEM转换率**: 1 ÷ 10 = 0.100 GEM/牛奶

## 影响范围

### ✅ 已升级的功能
- 牛奶生产速率验证
- 牛奶消耗速率验证
- GEM转换率验证
- 严格批量更新的所有验证逻辑

### 🔄 方法签名变更
- `calculateMilkProductionPerSecond()` - 现在是异步方法
- `calculateMilkConsumptionPerSecond()` - 现在是异步方法

### 📈 性能考虑
- 每次验证都会查询配置表
- 建议后续添加配置缓存机制
- 降级方案确保系统稳定性

## 部署注意事项

1. **配置表完整性**: 确保 `farm_configs` 和 `delivery_line_configs` 表有完整数据
2. **缓存更新**: 可能需要清理相关的Redis缓存
3. **监控日志**: 关注降级方案的警告日志
4. **性能监控**: 监控配置查询的性能影响

## 后续优化建议

1. **添加配置缓存**: 减少数据库查询次数
2. **批量配置获取**: 一次获取多个等级的配置
3. **配置预加载**: 应用启动时预加载常用配置
4. **监控告警**: 配置获取失败时的告警机制

## 相关文件

- `src/services/strictBatchResourceUpdateService.ts` - ✅ 已修改
- `src/models/DeliveryLineConfig.ts` - 出货线配置模型
- `src/services/farmConfigService.ts` - 牧场配置服务
- `scripts/test-strict-batch-update-config.js` - 测试脚本
- `docs/strict-batch-update-config-upgrade.md` - 本文档

## 总结

✅ **升级完成**: `/api/wallet/strict-batch-update-resources` 接口现在使用新配置表
✅ **验证通过**: 所有计算逻辑使用正确的配置数据
✅ **降级安全**: 配置获取失败时有完整的降级方案
✅ **性能稳定**: 异步配置获取不影响接口响应

现在严格批量更新服务已经完全基于配置表进行计算和验证！🎯
