# 宝箱测试附加 API 文档

本文档描述了用于测试宝箱功能的附加API接口。这些接口仅用于测试环境，可以帮助开发人员和测试人员更方便地测试宝箱相关功能。

## 基本信息

- 基础路径: `/api/test-chest`
- 所有接口都需要用户身份验证

## API 接口列表


### 2. 直接设置用户referralCount字段值

用于测试不同邀请数量下的奖励机制，直接设置用户的referralCount值。

**请求方法**: POST

**路径**: `/api/test-chest/increase-referral-count`

**请求参数**:

| 参数名 | 类型 | 必填 | 描述 |
| --- | --- | --- | --- |
| value | Number | 否 | 要设置的邀请数量值，默认为0 |

**请求示例**:

```json
{
  "value": 5
}
```

**响应示例**:

```json
{
  "ok": true,
  "message": "成功设置邀请数量为5",
  "data": {
    "userId": 123,
    "previousCount": 10,
    "newCount": 5
  }
}
```

## 使用示例

以下是一些使用场景示例：

### 测试每日推荐宝箱

1. 用户已经领取了今天的每日推荐宝箱
2. 调用`reset-daily-chest-claim`接口重置领取状态
3. 用户可以再次领取今天的每日推荐宝箱

### 测试邀请奖励机制

1. 调用`increase-referral-count`接口设置用户的邀请数量
2. 测试不同邀请数量下的奖励发放情况
3. 验证邀请奖励的正确性