# 测试重置游戏状态 API 文档

## 概述

测试重置游戏状态 API 是为 Wolf Fun 游戏开发的专用测试接口，用于在开发环境下重置用户的游戏状态，便于测试和调试。

## 安全特性

- **身份验证**: 需要有效的 JWT token
- **速率限制**: 每分钟最多 5 次重置操作
- **审计日志**: 所有重置操作都会被详细记录
- **环境记录**: 记录所有操作的环境信息

## API 接口

### 1. 健康检查

**接口**: `GET /api/test/health`

**描述**: 检查测试重置服务的健康状态

**请求头**: 无需身份验证

**响应示例**:
```json
{
  "ok": true,
  "data": {
    "status": "ok",
    "environment": "development",
    "isDevelopment": true,
    "timestamp": "2024-01-01T00:00:00.000Z",
    "uptime": 3600
  }
}
```

### 2. 获取重置安全检查信息

**接口**: `GET /api/test/reset-safety-info`

**描述**: 获取用户当前游戏状态的详细信息，用于重置前的安全检查

**请求头**: 
- `Authorization: Bearer <JWT_TOKEN>`

**响应示例**:
```json
{
  "ok": true,
  "data": {
    "environment": "development",
    "isDevelopment": true,
    "userExists": true,
    "farmPlotsCount": 20,
    "hasDeliveryLine": true,
    "userWalletInfo": {
      "id": 123,
      "walletAddress": "0x...",
      "createdAt": "2024-01-01T00:00:00.000Z",
      "lastActiveTime": "2024-01-01T12:00:00.000Z"
    },
    "farmPlotsDetails": {
      "unlockedCount": 3,
      "totalLevels": 25,
      "totalAccumulatedMilk": 150.500
    },
    "deliveryLineDetails": {
      "level": 5,
      "pendingMilk": 25.750,
      "pendingBlocks": 3
    }
  }
}
```

### 3. 重置游戏状态

**接口**: `POST /api/test/reset-game-state`

**描述**: 重置用户的完整游戏状态，包括农场区块和配送线

**请求头**:
- `Authorization: Bearer <JWT_TOKEN>`

**请求体**: `{}` (空对象)

**响应示例**:
```json
{
  "ok": true,
  "data": {
    "resetTimestamp": "2024-01-01T12:00:00.000Z"
  },
  "message": "游戏状态重置成功"
}
```

## 重置规则

### 农场区块重置规则

1. **等级重置**: 所有农场区块等级重置为 1
2. **解锁状态**: 只有第 1 号农场区块保持解锁状态，其他全部锁定
3. **牛舍数量**: 第 1 号农场区块牛舍数量为 1，其他为 0
4. **产量**: 第 1 号农场区块产量为 1，其他为 0
5. **生产速度**: 所有农场区块生产速度重置为 5 秒
6. **升级费用**: 所有农场区块升级费用重置为 200 GEM
7. **解锁费用**: 按公式重新计算 (2000 × 2^(编号-2))
8. **累积牛奶**: 清零

### 配送线重置规则

1. **等级重置**: 配送线等级重置为 1
2. **出货速度**: 重置为 5 秒/次
3. **方块容量**: 重置为 5 牛奶/方块
4. **方块价格**: 重置为 5 GEM/方块
5. **升级费用**: 重置为 500 GEM
6. **待处理数据**: 清空所有待处理牛奶和方块

## 使用示例

### 使用 curl

```bash
# 1. 健康检查
curl -X GET http://localhost:3000/api/test/health

# 2. 获取安全检查信息
curl -X GET http://localhost:3000/api/test/reset-safety-info \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"

# 3. 重置游戏状态
curl -X POST http://localhost:3000/api/test/reset-game-state \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{}'
```

### 使用 JavaScript

```javascript
const axios = require('axios');

const apiClient = axios.create({
  baseURL: 'http://localhost:3000',
  headers: {
    'Authorization': 'Bearer YOUR_JWT_TOKEN',
    'Content-Type': 'application/json'
  }
});

// 重置游戏状态
async function resetGameState() {
  try {
    const response = await apiClient.post('/api/test/reset-game-state', {});
    console.log('重置成功:', response.data);
  } catch (error) {
    console.error('重置失败:', error.response?.data || error.message);
  }
}
```

## 错误处理

### 常见错误码

- **400 Bad Request**: 参数验证失败
- **401 Unauthorized**: 身份验证失败
- **403 Forbidden**: 权限不足
- **404 Not Found**: 用户不存在
- **429 Too Many Requests**: 速率限制触发
- **500 Internal Server Error**: 服务器内部错误

### 错误响应示例

```json
{
  "ok": false,
  "message": "此功能仅在开发环境下可用"
}
```

## 测试脚本

项目根目录下的 `test_reset_api.js` 提供了完整的测试脚本：

```bash
# 安装依赖
npm install axios

# 运行测试（需要先修改脚本中的 JWT token）
node test_reset_api.js
```

## 注意事项

1. **数据不可恢复**: 重置操作会永久删除用户的游戏进度
2. **速率限制**: 每分钟最多 5 次重置操作
3. **审计日志**: 所有操作都会被记录，包括 IP 地址和用户代理
4. **事务安全**: 重置操作使用数据库事务，确保数据一致性
5. **环境记录**: 所有操作都会记录当前环境信息

## 日志记录

所有重置操作都会生成详细的审计日志，包括：

- 操作时间戳
- 用户信息
- 重置前后的状态对比
- 请求来源信息
- 执行时间
- 操作结果

日志示例：
```
[INFO] 游戏重置审计日志 {
  "auditType": "GAME_RESET",
  "walletId": 123,
  "operation": "FULL_GAME_STATE_RESET",
  "beforeState": { ... },
  "afterState": { ... },
  "requestInfo": {
    "requestId": "reset_1704110400000_abc123def",
    "ip": "127.0.0.1",
    "userAgent": "Mozilla/5.0...",
    "timestamp": "2024-01-01T12:00:00.000Z"
  }
}
```
