module.exports = {
  apps: [
    {
      name: 'wolf-fun-api',
      script: 'dist/app.js',
      instances: 'max',
      exec_mode: 'cluster',
      env: {
        NODE_ENV: 'production',
        PORT: 3456
      },
      env_development: {
        NODE_ENV: 'development',
        PORT: 3456,
        LOG_LEVEL: 'debug'
      },
      env_staging: {
        NODE_ENV: 'staging',
        PORT: 3456,
        LOG_LEVEL: 'info'
      },
      // 日志配置
      error_file: './logs/err.log',
      out_file: './logs/out.log',
      log_file: './logs/combined.log',
      time: true,
      
      // 性能配置
      max_memory_restart: '1G',
      node_args: '--max-old-space-size=1024',
      
      // 重启配置
      autorestart: true,
      watch: false,
      max_restarts: 10,
      min_uptime: '10s',
      
      // 健康检查
      health_check_grace_period: 3000,
      health_check_fatal_exceptions: true,
      
      // 环境变量
      source_map_support: true,
      
      // 进程管理
      kill_timeout: 5000,
      listen_timeout: 3000,
      
      // 合并日志
      merge_logs: true,
      
      // 实例配置
      instance_var: 'INSTANCE_ID'
    }
  ],

  deploy: {
    production: {
      user: 'deploy',
      host: 'your-server.com',
      ref: 'origin/main',
      repo: '**************:your-username/wolf_fun.git',
      path: '/var/www/wolf_fun',
      'pre-deploy-local': '',
      'post-deploy': 'npm install && npm run build && npm run migrate && pm2 reload ecosystem.config.js --env production',
      'pre-setup': '',
      'ssh_options': 'ForwardAgent=yes'
    },
    
    staging: {
      user: 'deploy',
      host: 'staging-server.com',
      ref: 'origin/develop',
      repo: '**************:your-username/wolf_fun.git',
      path: '/var/www/wolf_fun_staging',
      'post-deploy': 'npm install && npm run build && npm run migrate && pm2 reload ecosystem.config.js --env staging',
      'ssh_options': 'ForwardAgent=yes'
    }
  }
};
