// 获取测试用的JWT token脚本
const axios = require('axios');

const BASE_URL = 'http://localhost:3456';

// 测试用户信息
const testUser = {
  walletAddress: '******************************************', // 测试钱包地址
  signature: 'test_signature', // 测试签名
  message: 'test_message' // 测试消息
};

async function getTestToken() {
  console.log('🔑 尝试获取测试JWT token...');
  
  try {
    // 方法1: 尝试使用钱包登录接口
    console.log('📝 方法1: 尝试钱包登录...');
    const loginResponse = await axios.post(`${BASE_URL}/api/auth/wallet-login`, {
      walletAddress: testUser.walletAddress,
      signature: testUser.signature,
      message: testUser.message
    });
    
    if (loginResponse.data.ok && loginResponse.data.data.token) {
      console.log('✅ 钱包登录成功！');
      console.log(`Token: ${loginResponse.data.data.token}`);
      return loginResponse.data.data.token;
    }
    
  } catch (error) {
    console.log('❌ 钱包登录失败:', error.response?.data?.message || error.message);
  }
  
  try {
    // 方法2: 尝试使用用户注册接口
    console.log('📝 方法2: 尝试用户注册...');
    const registerResponse = await axios.post(`${BASE_URL}/api/auth/register`, {
      walletAddress: testUser.walletAddress,
      signature: testUser.signature,
      message: testUser.message
    });
    
    if (registerResponse.data.ok && registerResponse.data.data.token) {
      console.log('✅ 用户注册成功！');
      console.log(`Token: ${registerResponse.data.data.token}`);
      return registerResponse.data.data.token;
    }
    
  } catch (error) {
    console.log('❌ 用户注册失败:', error.response?.data?.message || error.message);
  }
  
  try {
    // 方法3: 尝试查看现有的测试用户
    console.log('📝 方法3: 查看测试接口...');
    const testResponse = await axios.get(`${BASE_URL}/api/test/users`);
    
    if (testResponse.data.ok && testResponse.data.data.length > 0) {
      const testUser = testResponse.data.data[0];
      console.log('✅ 找到测试用户:', testUser);
      
      // 尝试为测试用户生成token
      if (testUser.walletId) {
        console.log('🔄 尝试使用测试用户信息登录...');
        // 这里需要根据实际的认证逻辑来获取token
      }
    }
    
  } catch (error) {
    console.log('❌ 查看测试接口失败:', error.response?.data?.message || error.message);
  }
  
  // 方法4: 提供手动token输入选项
  console.log('\n💡 如果以上方法都失败，您可以：');
  console.log('1. 手动登录游戏获取有效的JWT token');
  console.log('2. 从浏览器开发者工具中复制Authorization header的token');
  console.log('3. 使用现有的测试token（如果有的话）');
  console.log('\n📋 将token复制到测试脚本中的JWT_TOKEN变量即可进行测试');
  
  return null;
}

// 验证token是否有效
async function validateToken(token) {
  if (!token) return false;
  
  try {
    console.log('\n🔍 验证token有效性...');
    const response = await axios.get(`${BASE_URL}/api/wallet/info`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    if (response.data.ok) {
      console.log('✅ Token有效！');
      console.log('用户信息:', response.data.data);
      return true;
    }
    
  } catch (error) {
    console.log('❌ Token无效:', error.response?.data?.message || error.message);
  }
  
  return false;
}

// 主函数
async function main() {
  console.log('🚀 开始获取测试JWT token');
  console.log('='.repeat(50));
  
  const token = await getTestToken();
  
  if (token) {
    const isValid = await validateToken(token);
    if (isValid) {
      console.log('\n🎉 成功获取有效的JWT token！');
      console.log('='.repeat(50));
      console.log('请将以下token复制到测试脚本中：');
      console.log(`JWT_TOKEN = '${token}';`);
      console.log('='.repeat(50));
    }
  } else {
    console.log('\n⚠️  无法自动获取token，请手动获取');
  }
}

main().catch(console.error);
