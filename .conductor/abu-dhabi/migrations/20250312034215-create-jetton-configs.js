'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const tables = await queryInterface.showAllTables();
    if (!tables.includes('jetton_configs')) {
      await queryInterface.createTable('jetton_configs', {
        id: {
          type: Sequelize.INTEGER.UNSIGNED,
          autoIncrement: true,
          primaryKey: true,
        },
        name: {
          type: Sequelize.STRING,
          allowNull: false,
          unique: true,
        },
        address: {
          type: Sequelize.STRING,
          allowNull: false,
          unique: true,
        },
        decimals: {
          type: Sequelize.INTEGER,
          allowNull: false,
          defaultValue: 6,
        },
        isActive: {
          type: Sequelize.BOOLEAN,
          defaultValue: true,
          allowNull: false,
        },
        description: {
          type: Sequelize.STRING,
          allowNull: true,
        },
        createdAt: {
          type: Sequelize.DATE,
          allowNull: false,
        },
        updatedAt: {
          type: Sequelize.DATE,
          allowNull: false,
        },
      });

      // 检查默认USDT配置是否存在
      const existingConfig = await queryInterface.sequelize.query(
        `SELECT * FROM jetton_configs WHERE name = 'USDT'`,
        { type: queryInterface.sequelize.QueryTypes.SELECT }
      );

      if (existingConfig.length === 0) {
        // 添加默认的USDT配置
        await queryInterface.bulkInsert('jetton_configs', [{
          name: 'USDT',
          address: 'kQD0GKBM8ZbryVk2aESmzfU6b9b_8era_IkvBSELujFZPsyy',
          decimals: 6,
          isActive: true,
          description: 'USDT Jetton',
          createdAt: new Date(),
          updatedAt: new Date()
        }]);
      }
    }
  },

  async down(queryInterface, Sequelize) {
    const tables = await queryInterface.showAllTables();
    if (tables.includes('jetton_configs')) {
      await queryInterface.dropTable('jetton_configs');
    }
  }
};
