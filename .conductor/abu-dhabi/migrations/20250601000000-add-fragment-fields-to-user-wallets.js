'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // 检查表是否存在
    const tables = await queryInterface.showAllTables();
    if (tables.includes('user_wallets')) {
      // 检查字段是否已存在
      const tableInfo = await queryInterface.describeTable('user_wallets');
      
      // 添加fragment_green字段（如果不存在）
      if (!tableInfo.fragment_green) {
        await queryInterface.addColumn('user_wallets', 'fragment_green', {
          type: Sequelize.INTEGER,
          defaultValue: 0,
          allowNull: true
        });
      }
      
      // 添加fragment_blue字段（如果不存在）
      if (!tableInfo.fragment_blue) {
        await queryInterface.addColumn('user_wallets', 'fragment_blue', {
          type: Sequelize.INTEGER,
          defaultValue: 0,
          allowNull: true
        });
      }
      
      // 添加fragment_purple字段（如果不存在）
      if (!tableInfo.fragment_purple) {
        await queryInterface.addColumn('user_wallets', 'fragment_purple', {
          type: Sequelize.INTEGER,
          defaultValue: 0,
          allowNull: true
        });
      }
      
      // 添加fragment_gold字段（如果不存在）
      if (!tableInfo.fragment_gold) {
        await queryInterface.addColumn('user_wallets', 'fragment_gold', {
          type: Sequelize.INTEGER,
          defaultValue: 0,
          allowNull: true
        });
      }
    }
  },

  async down(queryInterface, Sequelize) {
    // 检查表是否存在
    const tables = await queryInterface.showAllTables();
    if (tables.includes('user_wallets')) {
      // 移除添加的字段
      await queryInterface.removeColumn('user_wallets', 'fragment_green');
      await queryInterface.removeColumn('user_wallets', 'fragment_blue');
      await queryInterface.removeColumn('user_wallets', 'fragment_purple');
      await queryInterface.removeColumn('user_wallets', 'fragment_gold');
    }
  }
};