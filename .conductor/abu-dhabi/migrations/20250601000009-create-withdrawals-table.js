'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    const tables = await queryInterface.showAllTables();
    if (!tables.includes('withdrawals')) {
      await queryInterface.createTable('withdrawals', {
      id: {
        type: Sequelize.INTEGER.UNSIGNED,
        autoIncrement: true,
        primaryKey: true,
      },
      userId: {
        type: Sequelize.INTEGER.UNSIGNED,
        allowNull: false,
      },
      walletId: {
        type: Sequelize.INTEGER.UNSIGNED,
        allowNull: false,
      },
      currency: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      amount: {
        type: Sequelize.DECIMAL(18, 6),
        allowNull: false,
      },
      fee: {
        type: Sequelize.DECIMAL(18, 6),
        allowNull: false,
      },
      status: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      withdrawalAddress: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      remark: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      processedAt: {
        type: Sequelize.DATE,
        allowNull: true,
      },
      processedBy: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
      },
    });

      // 添加索引
      const indexes = await queryInterface.showIndex('withdrawals');
      const existingIndexes = indexes.map(index => index.name);

      if (!existingIndexes.includes('withdrawals_user_id')) {
        await queryInterface.addIndex('withdrawals', ['userId'], { name: 'withdrawals_user_id' });
      }
      if (!existingIndexes.includes('withdrawals_wallet_id')) {
        await queryInterface.addIndex('withdrawals', ['walletId'], { name: 'withdrawals_wallet_id' });
      }
      if (!existingIndexes.includes('withdrawals_currency')) {
        await queryInterface.addIndex('withdrawals', ['currency'], { name: 'withdrawals_currency' });
      }
      if (!existingIndexes.includes('withdrawals_status')) {
        await queryInterface.addIndex('withdrawals', ['status'], { name: 'withdrawals_status' });
      }
      if (!existingIndexes.includes('withdrawals_created_at')) {
        await queryInterface.addIndex('withdrawals', ['createdAt'], { name: 'withdrawals_created_at' });
      }
    }
  },

  down: async (queryInterface, Sequelize) => {
    const tables = await queryInterface.showAllTables();
    if (tables.includes('withdrawals')) {
      await queryInterface.dropTable('withdrawals');
    }
  }
};