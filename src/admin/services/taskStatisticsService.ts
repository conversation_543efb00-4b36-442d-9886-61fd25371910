import { Op, QueryTypes } from 'sequelize';
import { Tasks } from '../../models/Tasks';
import { UserTaskComplete } from '../../models/UserTaskComplete';
import { TaskStatistics, DateRangeQuery } from '../types/statistics';
import { logger, formatError } from '../../utils/logger';
import { sequelize } from '../../config/db';

export class TaskStatisticsService {
  /**
   * 获取任务统计数据
   */
  public static async getTaskStatistics(
    dateRange?: DateRangeQuery
  ): Promise<TaskStatistics> {
    try {
      // 获取总任务完成次数
      const totalTaskCompletions = await this.getTotalTaskCompletions(dateRange);
      
      // 获取各任务完成情况
      const taskCompletions = await this.getTaskCompletions(dateRange);

      return {
        totalTaskCompletions,
        taskCompletions,
      };
    } catch (error) {
      logger.error('获取任务统计失败:', formatError(error));
      throw error;
    }
  }

  /**
   * 获取总任务完成次数
   */
  private static async getTotalTaskCompletions(dateRange?: DateRangeQuery): Promise<number> {
    try {
      let whereClause = '';
      
      if (dateRange?.startDate && dateRange?.endDate) {
        whereClause = `WHERE completeTime >= :startDate AND completeTime <= :endDate`;
      }
      
      const result = await sequelize.query(`
        SELECT COUNT(*) as total
        FROM user_task_complete
        ${whereClause}
      `, {
        replacements: {
          startDate: dateRange?.startDate,
          endDate: dateRange?.endDate,
        },
        type: QueryTypes.SELECT,
      }) as any[];
      
      return result[0]?.total || 0;
    } catch (error) {
      logger.error('获取总任务完成次数失败:', formatError(error));
      return 0;
    }
  }

  /**
   * 获取各任务完成情况
   */
  private static async getTaskCompletions(dateRange?: DateRangeQuery): Promise<any[]> {
    try {
      let whereClause = '';
      
      if (dateRange?.startDate && dateRange?.endDate) {
        whereClause = `AND utc.completeTime >= :startDate AND utc.completeTime <= :endDate`;
      }
      
      const result = await sequelize.query(`
        SELECT 
          t.id as taskId,
          t.name as taskName,
          COUNT(DISTINCT utc.walletId) as completedUsers
        FROM tasks t
        LEFT JOIN user_task_complete utc ON t.id = utc.taskId
        WHERE 1=1 ${whereClause}
        GROUP BY t.id, t.name
        ORDER BY completedUsers DESC
      `, {
        replacements: {
          startDate: dateRange?.startDate,
          endDate: dateRange?.endDate,
        },
        type: QueryTypes.SELECT,
      });
      
      return result;
    } catch (error) {
      logger.error('获取各任务完成情况失败:', formatError(error));
      return [];
    }
  }

  /**
   * 获取每日任务完成趋势
   */
  public static async getDailyTaskCompletionTrend(days: number = 30): Promise<any[]> {
    try {
      const endDate = new Date();
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - days);
      
      const result = await sequelize.query(`
        SELECT 
          DATE(completeTime) as date,
          COUNT(*) as completions,
          COUNT(DISTINCT walletId) as uniqueUsers
        FROM user_task_complete
        WHERE completeTime >= :startDate AND completeTime <= :endDate
        GROUP BY DATE(completeTime)
        ORDER BY date ASC
      `, {
        replacements: { startDate, endDate },
        type: QueryTypes.SELECT,
      });
      
      return result;
    } catch (error) {
      logger.error('获取每日任务完成趋势失败:', formatError(error));
      return [];
    }
  }

  /**
   * 获取任务完成率
   */
  public static async getTaskCompletionRate(): Promise<any[]> {
    try {
      const result = await sequelize.query(`
        SELECT 
          t.id as taskId,
          t.name as taskName,
          t.type as taskType,
          COUNT(DISTINCT utc.walletId) as completedUsers,
          (SELECT COUNT(*) FROM user_wallets) as totalUsers,
          ROUND(COUNT(DISTINCT utc.walletId) * 100.0 / (SELECT COUNT(*) FROM user_wallets), 2) as completionRate
        FROM tasks t
        LEFT JOIN user_task_complete utc ON t.id = utc.taskId
        GROUP BY t.id, t.name, t.type
        ORDER BY completionRate DESC
      `, {
        type: QueryTypes.SELECT,
      });
      
      return result;
    } catch (error) {
      logger.error('获取任务完成率失败:', formatError(error));
      return [];
    }
  }

  /**
   * 获取任务类型统计
   */
  public static async getTaskTypeStats(): Promise<any[]> {
    try {
      const result = await sequelize.query(`
        SELECT 
          t.type as taskType,
          COUNT(DISTINCT t.id) as taskCount,
          COUNT(utc.id) as totalCompletions,
          COUNT(DISTINCT utc.walletId) as uniqueUsers
        FROM tasks t
        LEFT JOIN user_task_complete utc ON t.id = utc.taskId
        GROUP BY t.type
        ORDER BY totalCompletions DESC
      `, {
        type: QueryTypes.SELECT,
      });
      
      return result;
    } catch (error) {
      logger.error('获取任务类型统计失败:', formatError(error));
      return [];
    }
  }
}
