import { Request, Response } from 'express';
import jwt from 'jsonwebtoken';
import { v4 as uuidv4 } from 'uuid';
import { AdminUser, AdminRole, AdminStatus } from '../models/AdminUser';
import { AdminSession } from '../models/AdminSession';
import { successResponse, errorResponse } from '../utils/responseUtil';
import { logger } from '../utils/logger';
import { tFromRequest } from '../i18n';

// JWT密钥配置
const JWT_SECRET = process.env.JWT_SECRET_ADMIN || 'admin_jwt_secret_change_in_production';
const ACCESS_TOKEN_EXPIRES = '2h';
const REFRESH_TOKEN_EXPIRES = '7d';

// 登录请求接口
interface LoginRequest {
  username: string;
  password: string;
}

// 修改密码请求接口
interface ChangePasswordRequest {
  oldPassword: string;
  newPassword: string;
}

// JWT载荷接口
interface AdminJWTPayload {
  adminId: number;
  username: string;
  role: AdminRole;
  tokenId: string;
  type: 'access' | 'refresh';
}

// 导入管理员请求类型
import { AdminRequest } from '../types/customRequest';

export class AdminAuthController {
  
  /**
   * 管理员登录
   * POST /api/admin/auth/login
   */
  public async login(req: Request, res: Response): Promise<void> {
    try {
      const { username, password }: LoginRequest = req.body;

      // 验证输入
      if (!username || !password) {
        res.status(400).json(errorResponse(
          tFromRequest(req, 'errors.missingCredentials') || '用户名和密码不能为空'
        ));
        return;
      }

      // 查找管理员
      const admin = await AdminUser.findByUsername(username);
      if (!admin) {
        res.status(401).json(errorResponse(
          tFromRequest(req, 'errors.invalidCredentials') || '用户名或密码错误'
        ));
        return;
      }

      // 检查账户状态
      if (!admin.isActive()) {
        let message = '账户不可用';
        
        if (admin.status === AdminStatus.INACTIVE) {
          message = '账户已被禁用';
        } else if (admin.isLocked()) {
          message = admin.lockedUntil 
            ? `账户已被锁定，请在 ${admin.lockedUntil.toLocaleString()} 后重试`
            : '账户已被锁定';
        }

        res.status(403).json(errorResponse(message));
        return;
      }

      // 验证密码
      const isPasswordValid = await admin.validatePassword(password);
      if (!isPasswordValid) {
        // 增加失败次数
        await admin.incrementFailCount();
        
        res.status(401).json(errorResponse(
          tFromRequest(req, 'errors.invalidCredentials') || '用户名或密码错误'
        ));
        return;
      }

      // 登录成功，重置失败次数
      await admin.resetFailCount();
      
      // 获取客户端信息
      const ipAddress = req.ip || req.connection.remoteAddress || '未知';
      const userAgent = req.get('User-Agent') || '未知';

      // 限制并发会话数量
      await AdminSession.enforceSessionLimit(admin.id, 3);

      // 生成Token ID
      const tokenId = uuidv4();

      // 创建JWT Token
      const accessToken = this.generateAccessToken({
        adminId: admin.id,
        username: admin.username,
        role: admin.role,
        tokenId
      });

      const refreshToken = this.generateRefreshToken({
        adminId: admin.id,
        username: admin.username,
        role: admin.role,
        tokenId
      });

      // 创建会话记录
      const expiresAt = new Date(Date.now() + 2 * 60 * 60 * 1000); // 2小时后过期
      await AdminSession.createSession({
        adminId: admin.id,
        tokenId,
        ipAddress,
        userAgent,
        expiresAt
      });

      // 更新最后登录时间
      await admin.updateLastLogin();

      // 记录登录日志
      logger.info('管理员登录成功', {
        adminId: admin.id,
        username: admin.username,
        ipAddress,
        userAgent
      });

      res.json(successResponse({
        admin: admin.getSafeInfo(),
        accessToken,
        expiresIn: ACCESS_TOKEN_EXPIRES
      }, '登录成功'));

    } catch (error: any) {
      logger.error('管理员登录失败', { 
        error: error instanceof Error ? error.message : error,
        stack: error instanceof Error ? error.stack : undefined
      });
      res.status(500).json(errorResponse(
        tFromRequest(req, 'errors.serverError') || '服务器内部错误'
      ));
    }
  }

  /**
   * 管理员登出
   * POST /api/admin/auth/logout
   */
  public async logout(req: AdminRequest, res: Response): Promise<void> {
    try {
      const admin = req.admin;
      if (!admin) {
        res.status(401).json(errorResponse('未登录'));
        return;
      }

      // 查找并销毁会话
      const session = await AdminSession.findValidSession(admin.tokenId);
      if (session) {
        await session.destroy();
      }

      // 记录登出日志
      logger.info('管理员登出', {
        adminId: admin.adminId,
        username: admin.username,
        tokenId: admin.tokenId
      });

      res.json(successResponse({}, '登出成功'));

    } catch (error: any) {
      logger.error('管理员登出失败', { error: error instanceof Error ? error.message : error });
      res.status(500).json(errorResponse('登出失败'));
    }
  }

  /**
   * 刷新Token
   * POST /api/admin/auth/refresh
   */
  public async refreshToken(req: Request, res: Response): Promise<void> {
    try {
      const { refreshToken } = req.body;

      if (!refreshToken) {
        res.status(400).json(errorResponse('缺少刷新令牌'));
        return;
      }

      // 验证refresh token
      const payload = jwt.verify(refreshToken, JWT_SECRET) as AdminJWTPayload;
      
      if (payload.type !== 'refresh') {
        res.status(400).json(errorResponse('无效的刷新令牌'));
        return;
      }

      // 检查会话是否有效
      const session = await AdminSession.findValidSession(payload.tokenId);
      if (!session) {
        res.status(401).json(errorResponse('会话已过期或无效'));
        return;
      }

      // 检查管理员是否仍然有效
      const admin = await AdminUser.findByPk(payload.adminId);
      if (!admin || !admin.isActive()) {
        res.status(401).json(errorResponse('账户不可用'));
        return;
      }

      // 生成新的Access Token
      const newAccessToken = this.generateAccessToken({
        adminId: admin.id,
        username: admin.username,
        role: admin.role,
        tokenId: payload.tokenId
      });

      // 延长会话有效期
      await session.extendExpiry(2);
      await session.updateActivity();

      res.json(successResponse({
        accessToken: newAccessToken,
        expiresIn: ACCESS_TOKEN_EXPIRES
      }, 'Token刷新成功'));

    } catch (error: any) {
      logger.error('Token刷新失败', { error: error instanceof Error ? error.message : error });
      res.status(401).json(errorResponse('Token刷新失败'));
    }
  }

  /**
   * 获取当前管理员信息
   * GET /api/admin/auth/profile
   */
  public async getProfile(req: AdminRequest, res: Response): Promise<void> {
    try {
      const admin = req.admin;
      if (!admin) {
        res.status(401).json(errorResponse('未登录'));
        return;
      }

      // 获取完整的管理员信息
      const adminUser = await AdminUser.findByPk(admin.adminId);
      if (!adminUser) {
        res.status(404).json(errorResponse('管理员不存在'));
        return;
      }

      res.json(successResponse({
        admin: adminUser.getSafeInfo()
      }, '获取管理员信息成功'));

    } catch (error: any) {
      logger.error('获取管理员信息失败', { error: error instanceof Error ? error.message : error });
      res.status(500).json(errorResponse('获取管理员信息失败'));
    }
  }

  /**
   * 修改密码
   * POST /api/admin/auth/change-password
   */
  public async changePassword(req: AdminRequest, res: Response): Promise<void> {
    try {
      const admin = req.admin;
      if (!admin) {
        res.status(401).json(errorResponse('未登录'));
        return;
      }

      const { oldPassword, newPassword }: ChangePasswordRequest = req.body;

      // 验证输入
      if (!oldPassword || !newPassword) {
        res.status(400).json(errorResponse('旧密码和新密码不能为空'));
        return;
      }

      if (newPassword.length < 6) {
        res.status(400).json(errorResponse('新密码长度不能少于6位'));
        return;
      }

      // 获取管理员信息
      const adminUser = await AdminUser.findByPk(admin.adminId);
      if (!adminUser) {
        res.status(404).json(errorResponse('管理员不存在'));
        return;
      }

      // 验证旧密码
      const isOldPasswordValid = await adminUser.validatePassword(oldPassword);
      if (!isOldPasswordValid) {
        res.status(400).json(errorResponse('旧密码错误'));
        return;
      }

      // 设置新密码
      await adminUser.setPassword(newPassword);
      await adminUser.save();

      // 销毁所有会话，强制重新登录
      await AdminSession.destroyAllUserSessions(admin.adminId);

      // 记录密码修改日志
      logger.info('管理员修改密码', {
        adminId: admin.adminId,
        username: admin.username
      });

      res.json(successResponse({}, '密码修改成功，请重新登录'));

    } catch (error: any) {
      logger.error('修改密码失败', { error: error instanceof Error ? error.message : error });
      res.status(500).json(errorResponse('修改密码失败'));
    }
  }

  /**
   * 获取当前会话列表
   * GET /api/admin/auth/sessions
   */
  public async getSessions(req: AdminRequest, res: Response): Promise<void> {
    try {
      const admin = req.admin;
      if (!admin) {
        res.status(401).json(errorResponse('未登录'));
        return;
      }

      const sessions = await AdminSession.findAll({
        where: {
          adminId: admin.adminId,
          isActive: true
        },
        order: [['lastActivityAt', 'DESC']]
      });

      const sessionList = sessions.map(session => ({
        id: session.id,
        ipAddress: session.ipAddress,
        userAgent: session.userAgent,
        lastActivityAt: session.lastActivityAt,
        createdAt: session.createdAt,
        isCurrent: session.tokenId === admin.tokenId
      }));

      res.json(successResponse({
        sessions: sessionList,
        total: sessions.length
      }, '获取会话列表成功'));

    } catch (error: any) {
      logger.error('获取会话列表失败', { error: error instanceof Error ? error.message : error });
      res.status(500).json(errorResponse('获取会话列表失败'));
    }
  }

  /**
   * 生成Access Token
   */
  private generateAccessToken(payload: Omit<AdminJWTPayload, 'type'>): string {
    return jwt.sign(
      { ...payload, type: 'access' },
      JWT_SECRET,
      { expiresIn: ACCESS_TOKEN_EXPIRES }
    );
  }

  /**
   * 生成Refresh Token
   */
  private generateRefreshToken(payload: Omit<AdminJWTPayload, 'type'>): string {
    return jwt.sign(
      { ...payload, type: 'refresh' },
      JWT_SECRET,
      { expiresIn: REFRESH_TOKEN_EXPIRES }
    );
  }
}

// 创建控制器实例
const adminAuthController = new AdminAuthController();

// 导出控制器方法
export const login = (req: Request, res: Response) => adminAuthController.login(req, res);
export const logout = (req: AdminRequest, res: Response) => adminAuthController.logout(req, res);
export const refreshToken = (req: Request, res: Response) => adminAuthController.refreshToken(req, res);
export const getProfile = (req: AdminRequest, res: Response) => adminAuthController.getProfile(req, res);
export const changePassword = (req: AdminRequest, res: Response) => adminAuthController.changePassword(req, res);
export const getSessions = (req: AdminRequest, res: Response) => adminAuthController.getSessions(req, res);