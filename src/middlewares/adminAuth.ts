import { Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { AdminUser, AdminRole } from '../models/AdminUser';
import { AdminSession } from '../models/AdminSession';
import { AdminRequest } from '../types/customRequest';
import { errorResponse } from '../utils/responseUtil';
import { tFromRequest } from '../i18n';
import { logger } from '../utils/logger';

// JWT密钥配置
const JWT_SECRET = process.env.JWT_SECRET_ADMIN || 'admin_jwt_secret_change_in_production';

// JWT载荷接口
interface AdminJWTPayload {
  adminId: number;
  username: string;
  role: AdminRole;
  tokenId: string;
  type: 'access' | 'refresh';
  iat?: number;
  exp?: number;
}

/**
 * 管理员JWT认证中间件
 * 验证JWT Token并设置管理员信息到请求对象
 */
export function adminAuthMiddleware(req: AdminRequest, res: Response, next: NextFunction): void {
  try {
    // 1. 从请求头获取Token
    const authHeader = req.headers.authorization;

    if (!authHeader) {
      logger.warn('管理员认证失败: 缺少Authorization header', {
        path: req.path,
        method: req.method,
        ip: req.ip
      });
      res.status(401).json(errorResponse(
        tFromRequest(req, 'errors.noTokenProvided') || '请提供认证令牌'
      ));
      return;
    }

    // 2. 解析Bearer Token格式
    const tokenParts = authHeader.split(' ');
    if (tokenParts.length !== 2 || tokenParts[0] !== 'Bearer') {
      logger.warn('管理员认证失败: Token格式无效', {
        path: req.path,
        method: req.method,
        ip: req.ip
      });
      res.status(401).json(errorResponse(
        tFromRequest(req, 'errors.invalidTokenFormat') || '令牌格式无效'
      ));
      return;
    }

    const token = tokenParts[1];

    // 3. 验证JWT Token
    const payload = jwt.verify(token, JWT_SECRET) as AdminJWTPayload;

    // 4. 验证Token类型
    if (payload.type !== 'access') {
      logger.warn('管理员认证失败: Token类型无效', {
        tokenType: payload.type,
        path: req.path,
        method: req.method,
        ip: req.ip
      });
      res.status(401).json(errorResponse('令牌类型无效'));
      return;
    }

    // 5. 验证会话有效性
    AdminSession.findValidSession(payload.tokenId)
      .then(async (session) => {
        if (!session) {
          logger.warn('管理员认证失败: 会话不存在或已过期', {
            tokenId: payload.tokenId,
            adminId: payload.adminId,
            path: req.path,
            method: req.method,
            ip: req.ip
          });
          res.status(401).json(errorResponse('会话已过期，请重新登录'));
          return;
        }

        // 6. 验证管理员账户状态
        const admin = await AdminUser.findByPk(payload.adminId);
        if (!admin || !admin.isActive()) {
          logger.warn('管理员认证失败: 账户不可用', {
            adminId: payload.adminId,
            accountStatus: admin?.status,
            isLocked: admin?.isLocked(),
            path: req.path,
            method: req.method,
            ip: req.ip
          });
          res.status(403).json(errorResponse('账户不可用'));
          return;
        }

        // 7. 更新会话活动时间
        await session.updateActivity();

        // 8. 设置管理员信息到请求对象
        req.admin = {
          adminId: payload.adminId,
          username: payload.username,
          role: payload.role,
          tokenId: payload.tokenId
        };

        // 9. 记录操作日志（仅对重要操作）
        if (req.method !== 'GET') {
          logger.info('管理员操作', {
            adminId: payload.adminId,
            username: payload.username,
            method: req.method,
            path: req.path,
            ip: req.ip,
            userAgent: req.get('User-Agent')
          });
        }

        next();
      })
      .catch((error) => {
        logger.error('管理员认证失败: 数据库查询错误', {
          error: error instanceof Error ? error.message : String(error),
          tokenId: payload.tokenId,
          adminId: payload.adminId
        });
        res.status(500).json(errorResponse(
          tFromRequest(req, 'errors.serverError') || '服务器内部错误'
        ));
      });

  } catch (error: any) {
    // JWT验证失败
    if (error.name === 'JsonWebTokenError') {
      logger.warn('管理员认证失败: JWT验证失败', {
        error: error.message,
        path: req.path,
        method: req.method,
        ip: req.ip
      });
      res.status(401).json(errorResponse('令牌无效'));
      return;
    }

    if (error.name === 'TokenExpiredError') {
      logger.warn('管理员认证失败: JWT已过期', {
        path: req.path,
        method: req.method,
        ip: req.ip
      });
      res.status(401).json(errorResponse('令牌已过期，请重新登录'));
      return;
    }

    // 其他错误
    logger.error('管理员认证中间件错误', {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      path: req.path,
      method: req.method,
      ip: req.ip
    });
    
    res.status(500).json(errorResponse(
      tFromRequest(req, 'errors.serverError') || '认证服务错误'
    ));
  }
}

/**
 * 管理员角色权限验证中间件
 * 验证管理员是否具有指定的角色权限
 */
export function adminRoleMiddleware(requiredRole: AdminRole) {
  return (req: AdminRequest, res: Response, next: NextFunction): void => {
    try {
      const admin = req.admin;
      
      if (!admin) {
        res.status(401).json(errorResponse('未认证的管理员'));
        return;
      }

      // 检查角色权限
      const roleHierarchy = {
        [AdminRole.SUPER_ADMIN]: 3,
        [AdminRole.ADMIN]: 2,
        [AdminRole.OPERATOR]: 1
      };

      const currentRoleLevel = roleHierarchy[admin.role];
      const requiredRoleLevel = roleHierarchy[requiredRole];

      if (currentRoleLevel < requiredRoleLevel) {
        logger.warn('管理员权限不足', {
          adminId: admin.adminId,
          username: admin.username,
          currentRole: admin.role,
          requiredRole,
          path: req.path,
          method: req.method,
          ip: req.ip
        });
        
        res.status(403).json(errorResponse(
          `权限不足，需要 ${requiredRole} 或更高级别权限`
        ));
        return;
      }

      next();
    } catch (error: any) {
      logger.error('管理员角色验证失败', {
        error: error instanceof Error ? error.message : String(error),
        requiredRole,
        path: req.path,
        method: req.method,
        ip: req.ip
      });
      
      res.status(500).json(errorResponse('权限验证失败'));
    }
  };
}

/**
 * 安全操作确认中间件
 * 对于危险操作需要额外确认
 */
export function securityConfirmationMiddleware(req: AdminRequest, res: Response, next: NextFunction): void {
  try {
    // 定义危险操作
    const dangerousOperations = ['DELETE', 'PUT'];
    const dangerousPaths = ['/delete', '/remove', '/destroy', '/reset', '/clear'];
    
    const isDangerous = dangerousOperations.includes(req.method) || 
                       dangerousPaths.some(path => req.path.toLowerCase().includes(path));

    if (isDangerous) {
      // 检查确认标头
      const confirmHeader = req.get('X-Confirm-Operation');
      if (confirmHeader !== 'true') {
        res.status(400).json(errorResponse(
          '危险操作需要确认，请在请求头中添加 X-Confirm-Operation: true'
        ));
        return;
      }

      // 记录危险操作
      logger.warn('管理员执行危险操作', {
        adminId: req.admin?.adminId,
        username: req.admin?.username,
        method: req.method,
        path: req.path,
        body: req.body,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        confirmed: true
      });
    }

    next();
  } catch (error: any) {
    logger.error('安全确认中间件错误', {
      error: error instanceof Error ? error.message : String(error)
    });
    res.status(500).json(errorResponse('安全验证失败'));
  }
}

/**
 * 管理员操作频率限制中间件
 * 防止频繁操作
 */
const operationCounts = new Map<string, { count: number; resetTime: number }>();

export function adminRateLimitMiddleware(maxOperations: number = 30, windowMs: number = 60000) {
  return (req: AdminRequest, res: Response, next: NextFunction): void => {
    try {
      const admin = req.admin;
      if (!admin) {
        next();
        return;
      }

      const now = Date.now();
      const userKey = `admin:${admin.adminId}:${req.method}:${req.path}`;
      const userOps = operationCounts.get(userKey);

      if (!userOps || now > userOps.resetTime) {
        // 重置计数器
        operationCounts.set(userKey, {
          count: 1,
          resetTime: now + windowMs
        });
        next();
        return;
      }

      if (userOps.count >= maxOperations) {
        logger.warn('管理员操作频率超限', {
          adminId: admin.adminId,
          username: admin.username,
          path: req.path,
          method: req.method,
          count: userOps.count,
          limit: maxOperations
        });

        const remainingTime = Math.ceil((userOps.resetTime - now) / 1000);
        res.status(429).json(errorResponse(
          `操作过于频繁，请在 ${remainingTime} 秒后重试`
        ));
        return;
      }

      userOps.count++;
      next();
    } catch (error: any) {
      logger.error('管理员频率限制中间件错误', {
        error: error instanceof Error ? error.message : String(error)
      });
      next(); // 频率限制失败时允许通过，避免阻塞正常操作
    }
  };
}

/**
 * 开发环境管理员认证中间件（用于测试）
 */
export function devAdminAuthMiddleware(req: AdminRequest, res: Response, next: NextFunction): void {
  // 仅在开发环境使用
  if (process.env.NODE_ENV !== 'development') {
    adminAuthMiddleware(req, res, next);
    return;
  }

  try {
    // 开发环境下，如果没有认证信息，创建一个测试管理员
    if (!req.admin) {
      req.admin = {
        adminId: 1,
        username: 'dev_admin',
        role: AdminRole.SUPER_ADMIN,
        tokenId: 'dev_token_id'
      };
    }

    logger.debug('开发环境管理员认证', {
      admin: req.admin,
      path: req.path,
      method: req.method
    });

    next();
  } catch (error: any) {
    logger.error('开发环境管理员认证失败', {
      error: error instanceof Error ? error.message : String(error)
    });
    res.status(500).json(errorResponse('开发环境认证失败'));
  }
}