// migrations/20250804000000-create-admin-users-table.js
module.exports = {
  up: async (queryInterface, Sequelize) => {
    const tables = await queryInterface.showAllTables();
    
    // 创建 admin_users 表
    if (!tables.includes('admin_users')) {
      await queryInterface.createTable('admin_users', {
        id: {
          type: Sequelize.INTEGER.UNSIGNED,
          autoIncrement: true,
          primaryKey: true,
          allowNull: false,
          comment: '管理员ID'
        },
        username: {
          type: Sequelize.STRING(50),
          allowNull: false,
          unique: true,
          comment: '管理员用户名'
        },
        password: {
          type: Sequelize.STRING(255),
          allowNull: false,
          comment: '加密密码'
        },
        email: {
          type: Sequelize.STRING(100),
          allowNull: false,
          unique: true,
          comment: '邮箱地址'
        },
        realName: {
          type: Sequelize.STRING(50),
          allowNull: true,
          comment: '真实姓名'
        },
        role: {
          type: Sequelize.ENUM('super_admin', 'admin', 'operator'),
          allowNull: false,
          defaultValue: 'operator',
          comment: '管理员角色'
        },
        status: {
          type: Sequelize.ENUM('active', 'inactive', 'locked'),
          allowNull: false,
          defaultValue: 'active',
          comment: '账户状态'
        },
        lastLoginAt: {
          type: Sequelize.DATE,
          allowNull: true,
          comment: '最后登录时间'
        },
        loginFailCount: {
          type: Sequelize.INTEGER.UNSIGNED,
          allowNull: false,
          defaultValue: 0,
          comment: '登录失败次数'
        },
        lockedUntil: {
          type: Sequelize.DATE,
          allowNull: true,
          comment: '锁定到期时间'
        },
        createdBy: {
          type: Sequelize.INTEGER.UNSIGNED,
          allowNull: true,
          comment: '创建者ID'
        },
        createdAt: {
          type: Sequelize.DATE,
          allowNull: false,
          comment: '创建时间'
        },
        updatedAt: {
          type: Sequelize.DATE,
          allowNull: false,
          comment: '更新时间'
        }
      }, {
        comment: '管理员用户表'
      });

      // 添加索引
      await queryInterface.addIndex('admin_users', ['username'], {
        name: 'idx_username'
      });
      
      await queryInterface.addIndex('admin_users', ['email'], {
        name: 'idx_email'
      });
      
      await queryInterface.addIndex('admin_users', ['status'], {
        name: 'idx_status'
      });

      console.log('✅ admin_users 表创建成功');
    } else {
      console.log('⚠️  admin_users 表已存在，跳过创建');
    }
  },

  down: async (queryInterface, Sequelize) => {
    const tables = await queryInterface.showAllTables();
    if (tables.includes('admin_users')) {
      await queryInterface.dropTable('admin_users');
      console.log('✅ admin_users 表删除成功');
    }
  }
};