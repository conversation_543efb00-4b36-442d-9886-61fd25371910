// migrations/20250804000001-create-admin-sessions-table.js
module.exports = {
  up: async (queryInterface, Sequelize) => {
    const tables = await queryInterface.showAllTables();
    
    // 创建 admin_sessions 表
    if (!tables.includes('admin_sessions')) {
      await queryInterface.createTable('admin_sessions', {
        id: {
          type: Sequelize.INTEGER.UNSIGNED,
          autoIncrement: true,
          primaryKey: true,
          allowNull: false,
          comment: '会话ID'
        },
        adminId: {
          type: Sequelize.INTEGER.UNSIGNED,
          allowNull: false,
          comment: '管理员ID',
          references: {
            model: 'admin_users',
            key: 'id'
          },
          onUpdate: 'CASCADE',
          onDelete: 'CASCADE'
        },
        tokenId: {
          type: Sequelize.STRING(255),
          allowNull: false,
          unique: true,
          comment: 'JWT Token唯一标识符'
        },
        ipAddress: {
          type: Sequelize.STRING(45), // 支持IPv6
          allowNull: false,
          comment: '登录IP地址'
        },
        userAgent: {
          type: Sequelize.TEXT,
          allowNull: false,
          comment: '用户代理字符串'
        },
        expiresAt: {
          type: Sequelize.DATE,
          allowNull: false,
          comment: '过期时间'
        },
        isActive: {
          type: Sequelize.BOOLEAN,
          allowNull: false,
          defaultValue: true,
          comment: '是否活跃'
        },
        lastActivityAt: {
          type: Sequelize.DATE,
          allowNull: true,
          comment: '最后活动时间'
        },
        createdAt: {
          type: Sequelize.DATE,
          allowNull: false,
          comment: '创建时间'
        },
        updatedAt: {
          type: Sequelize.DATE,
          allowNull: false,
          comment: '更新时间'
        }
      }, {
        comment: '管理员会话表'
      });

      // 添加索引
      await queryInterface.addIndex('admin_sessions', ['tokenId'], {
        name: 'idx_token_id'
      });
      
      await queryInterface.addIndex('admin_sessions', ['adminId'], {
        name: 'idx_admin_id'
      });
      
      await queryInterface.addIndex('admin_sessions', ['expiresAt'], {
        name: 'idx_expires_at'
      });
      
      await queryInterface.addIndex('admin_sessions', ['adminId', 'isActive', 'expiresAt'], {
        name: 'idx_active_sessions'
      });

      console.log('✅ admin_sessions 表创建成功');
    } else {
      console.log('⚠️  admin_sessions 表已存在，跳过创建');
    }
  },

  down: async (queryInterface, Sequelize) => {
    const tables = await queryInterface.showAllTables();
    if (tables.includes('admin_sessions')) {
      await queryInterface.dropTable('admin_sessions');
      console.log('✅ admin_sessions 表删除成功');
    }
  }
};