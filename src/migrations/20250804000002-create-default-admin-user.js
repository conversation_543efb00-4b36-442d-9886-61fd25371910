// migrations/20250804000002-create-default-admin-user.js
const bcrypt = require('bcrypt');

module.exports = {
  up: async (queryInterface, Sequelize) => {
    const adminUsers = await queryInterface.sequelize.query(
      "SELECT COUNT(*) as count FROM admin_users WHERE username = 'admin'",
      { type: queryInterface.sequelize.QueryTypes.SELECT }
    );

    // 如果不存在默认管理员，则创建
    if (adminUsers[0].count === 0) {
      // 生成默认密码的哈希值
      const saltRounds = 12;
      const defaultPassword = process.env.DEFAULT_ADMIN_PASSWORD || 'admin123456';
      const hashedPassword = await bcrypt.hash(defaultPassword, saltRounds);

      await queryInterface.bulkInsert('admin_users', [{
        username: 'admin',
        password: hashedPassword,
        email: process.env.DEFAULT_ADMIN_EMAIL || '<EMAIL>',
        realName: '系统管理员',
        role: 'super_admin',
        status: 'active',
        loginFailCount: 0,
        createdAt: new Date(),
        updatedAt: new Date()
      }]);

      console.log('✅ 默认管理员账户创建成功');
      console.log('📋 用户名: admin');
      console.log(`📋 密码: ${defaultPassword}`);
      console.log('📋 角色: super_admin');
      console.log('⚠️  请尽快登录并修改默认密码！');
    } else {
      console.log('⚠️  默认管理员账户已存在，跳过创建');
    }
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.bulkDelete('admin_users', {
      username: 'admin'
    });
    console.log('✅ 默认管理员账户删除成功');
  }
};