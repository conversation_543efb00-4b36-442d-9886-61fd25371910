import { Model, DataTypes, Optional } from "sequelize";
import { sequelize } from "../config/db";
import { AdminUser } from "./AdminUser";

// 管理员会话属性接口
interface AdminSessionAttributes {
  id: number;
  adminId: number;
  tokenId: string;      // JWT Token的唯一标识符
  ipAddress: string;
  userAgent: string;
  expiresAt: Date;
  isActive: boolean;
  lastActivityAt?: Date;
  createdAt?: Date;
  updatedAt?: Date;
}

// 创建会话时的可选属性
type AdminSessionCreationAttributes = Optional<AdminSessionAttributes, 'id' | 'lastActivityAt' | 'createdAt' | 'updatedAt'>;

// 会话信息接口
export interface AdminSessionInfo {
  id: number;
  adminId: number;
  tokenId: string;
  ipAddress: string;
  userAgent: string;
  expiresAt: Date;
  isActive: boolean;
  lastActivityAt?: Date;
  createdAt?: Date;
}

export class AdminSession extends Model<AdminSessionAttributes, AdminSessionCreationAttributes> implements AdminSessionAttributes {
  public id!: number;
  public adminId!: number;
  public tokenId!: string;
  public ipAddress!: string;
  public userAgent!: string;
  public expiresAt!: Date;
  public isActive!: boolean;
  public lastActivityAt?: Date;

  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;

  /**
   * 检查会话是否有效
   */
  public isValid(): boolean {
    return this.isActive && this.expiresAt > new Date();
  }

  /**
   * 检查会话是否过期
   */
  public isExpired(): boolean {
    return this.expiresAt <= new Date();
  }

  /**
   * 更新最后活动时间
   */
  public async updateActivity(): Promise<void> {
    this.lastActivityAt = new Date();
    await this.save();
  }

  /**
   * 销毁会话（登出）
   */
  public async destroy(): Promise<void> {
    this.isActive = false;
    await this.save();
  }

  /**
   * 延长会话有效期
   */
  public async extendExpiry(hours: number = 2): Promise<void> {
    this.expiresAt = new Date(Date.now() + hours * 60 * 60 * 1000);
    await this.save();
  }

  /**
   * 获取会话信息
   */
  public getSessionInfo(): AdminSessionInfo {
    return {
      id: this.id,
      adminId: this.adminId,
      tokenId: this.tokenId,
      ipAddress: this.ipAddress,
      userAgent: this.userAgent,
      expiresAt: this.expiresAt,
      isActive: this.isActive,
      lastActivityAt: this.lastActivityAt,
      createdAt: this.createdAt
    };
  }

  /**
   * 静态方法：根据TokenId查找有效会话
   */
  public static async findValidSession(tokenId: string): Promise<AdminSession | null> {
    return this.findOne({
      where: {
        tokenId,
        isActive: true,
        expiresAt: {
          [sequelize.Sequelize.Op.gt]: new Date()
        }
      }
    });
  }

  /**
   * 静态方法：创建新会话
   */
  public static async createSession(data: {
    adminId: number;
    tokenId: string;
    ipAddress: string;
    userAgent: string;
    expiresAt: Date;
  }): Promise<AdminSession> {
    return this.create({
      adminId: data.adminId,
      tokenId: data.tokenId,
      ipAddress: data.ipAddress,
      userAgent: data.userAgent,
      expiresAt: data.expiresAt,
      isActive: true,
      lastActivityAt: new Date()
    });
  }

  /**
   * 静态方法：清理过期会话
   */
  public static async cleanupExpiredSessions(): Promise<number> {
    const result = await this.update(
      { isActive: false },
      {
        where: {
          expiresAt: {
            [sequelize.Sequelize.Op.lt]: new Date()
          }
        }
      }
    );
    return result[0]; // 返回更新的记录数
  }

  /**
   * 静态方法：销毁指定管理员的所有会话
   */
  public static async destroyAllUserSessions(adminId: number): Promise<number> {
    const result = await this.update(
      { isActive: false },
      {
        where: {
          adminId,
          isActive: true
        }
      }
    );
    return result[0];
  }

  /**
   * 静态方法：获取管理员的活跃会话数量
   */
  public static async getActiveSessionCount(adminId: number): Promise<number> {
    return this.count({
      where: {
        adminId,
        isActive: true,
        expiresAt: {
          [sequelize.Sequelize.Op.gt]: new Date()
        }
      }
    });
  }

  /**
   * 静态方法：限制并发会话数量
   */
  public static async enforceSessionLimit(adminId: number, maxSessions: number = 3): Promise<void> {
    const activeSessions = await this.findAll({
      where: {
        adminId,
        isActive: true,
        expiresAt: {
          [sequelize.Sequelize.Op.gt]: new Date()
        }
      },
      order: [['lastActivityAt', 'ASC']] // 按最后活动时间升序，优先清理最久未活动的会话
    });

    if (activeSessions.length >= maxSessions) {
      // 清理最老的会话，保留最新的 maxSessions-1 个会话
      const sessionsToDestroy = activeSessions.slice(0, activeSessions.length - maxSessions + 1);
      
      for (const session of sessionsToDestroy) {
        await session.destroy();
      }
    }
  }
}

// 初始化模型
AdminSession.init(
  {
    id: {
      type: DataTypes.INTEGER.UNSIGNED,
      autoIncrement: true,
      primaryKey: true,
    },
    adminId: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false,
      references: {
        model: AdminUser,
        key: 'id'
      },
      comment: '管理员ID'
    },
    tokenId: {
      type: DataTypes.STRING(255),
      allowNull: false,
      unique: true,
      comment: 'JWT Token唯一标识符'
    },
    ipAddress: {
      type: DataTypes.STRING(45), // 支持IPv6
      allowNull: false,
      comment: '登录IP地址'
    },
    userAgent: {
      type: DataTypes.TEXT,
      allowNull: false,
      comment: '用户代理字符串'
    },
    expiresAt: {
      type: DataTypes.DATE,
      allowNull: false,
      comment: '过期时间'
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
      comment: '是否活跃'
    },
    lastActivityAt: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: '最后活动时间'
    }
  },
  {
    tableName: "admin_sessions",
    sequelize,
    timestamps: true,
    indexes: [
      {
        name: 'idx_token_id',
        fields: ['tokenId']
      },
      {
        name: 'idx_admin_id',
        fields: ['adminId']
      },
      {
        name: 'idx_expires_at',
        fields: ['expiresAt']
      },
      {
        name: 'idx_active_sessions',
        fields: ['adminId', 'isActive', 'expiresAt']
      }
    ],
    comment: '管理员会话表'
  }
);

// 设置关联关系
AdminUser.hasMany(AdminSession, { foreignKey: 'adminId', as: 'sessions' });
AdminSession.belongsTo(AdminUser, { foreignKey: 'adminId', as: 'admin' });