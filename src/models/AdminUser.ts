import { Model, DataTypes, Optional } from "sequelize";
import { sequelize } from "../config/db";
import bcrypt from 'bcrypt';

// 管理员角色枚举
export enum AdminRole {
  SUPER_ADMIN = 'super_admin',
  ADMIN = 'admin',
  OPERATOR = 'operator'
}

// 管理员状态枚举
export enum AdminStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  LOCKED = 'locked'
}

// 管理员属性接口
interface AdminUserAttributes {
  id: number;
  username: string;
  password: string;
  email: string;
  realName?: string;
  role: AdminRole;
  status: AdminStatus;
  lastLoginAt?: Date;
  loginFailCount: number;
  lockedUntil?: Date;
  createdBy?: number;
  createdAt?: Date;
  updatedAt?: Date;
}

// 创建管理员时的可选属性
type AdminUserCreationAttributes = Optional<AdminUserAttributes, 'id' | 'lastLoginAt' | 'loginFailCount' | 'lockedUntil' | 'createdBy' | 'createdAt' | 'updatedAt'>;

// 管理员登录信息接口
export interface AdminLoginInfo {
  username: string;
  password: string;
}

// 管理员安全信息接口（不包含敏感信息）
export interface AdminSafeInfo {
  id: number;
  username: string;
  email: string;
  realName?: string;
  role: AdminRole;
  status: AdminStatus;
  lastLoginAt?: Date;
  createdAt?: Date;
}

export class AdminUser extends Model<AdminUserAttributes, AdminUserCreationAttributes> implements AdminUserAttributes {
  public id!: number;
  public username!: string;
  public password!: string;
  public email!: string;
  public realName?: string;
  public role!: AdminRole;
  public status!: AdminStatus;
  public lastLoginAt?: Date;
  public loginFailCount!: number;
  public lockedUntil?: Date;
  public createdBy?: number;

  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;

  /**
   * 验证密码
   */
  public async validatePassword(password: string): Promise<boolean> {
    return bcrypt.compare(password, this.password);
  }

  /**
   * 设置密码（加密）
   */
  public async setPassword(password: string): Promise<void> {
    const saltRounds = 12;
    this.password = await bcrypt.hash(password, saltRounds);
  }

  /**
   * 检查账户是否被锁定
   */
  public isLocked(): boolean {
    if (this.status === AdminStatus.LOCKED) {
      return true;
    }
    if (this.lockedUntil && this.lockedUntil > new Date()) {
      return true;
    }
    return false;
  }

  /**
   * 检查账户是否活跃
   */
  public isActive(): boolean {
    return this.status === AdminStatus.ACTIVE && !this.isLocked();
  }

  /**
   * 增加登录失败次数
   */
  public async incrementFailCount(): Promise<void> {
    this.loginFailCount += 1;
    
    // 如果失败次数达到5次，锁定账户30分钟
    if (this.loginFailCount >= 5) {
      this.lockedUntil = new Date(Date.now() + 30 * 60 * 1000); // 30分钟后解锁
    }
    
    await this.save();
  }

  /**
   * 重置登录失败次数
   */
  public async resetFailCount(): Promise<void> {
    this.loginFailCount = 0;
    this.lockedUntil = undefined;
    await this.save();
  }

  /**
   * 更新最后登录时间
   */
  public async updateLastLogin(): Promise<void> {
    this.lastLoginAt = new Date();
    await this.save();
  }

  /**
   * 获取安全信息（不包含密码等敏感信息）
   */
  public getSafeInfo(): AdminSafeInfo {
    return {
      id: this.id,
      username: this.username,
      email: this.email,
      realName: this.realName,
      role: this.role,
      status: this.status,
      lastLoginAt: this.lastLoginAt,
      createdAt: this.createdAt
    };
  }

  /**
   * 检查是否有指定权限
   */
  public hasPermission(requiredRole: AdminRole): boolean {
    const roleHierarchy = {
      [AdminRole.SUPER_ADMIN]: 3,
      [AdminRole.ADMIN]: 2,
      [AdminRole.OPERATOR]: 1
    };

    return roleHierarchy[this.role] >= roleHierarchy[requiredRole];
  }

  /**
   * 静态方法：根据用户名查找管理员
   */
  public static async findByUsername(username: string): Promise<AdminUser | null> {
    return this.findOne({
      where: { username }
    });
  }

  /**
   * 静态方法：创建新管理员
   */
  public static async createAdmin(data: {
    username: string;
    password: string;
    email: string;
    realName?: string;
    role: AdminRole;
    createdBy?: number;
  }): Promise<AdminUser> {
    const admin = new AdminUser({
      username: data.username,
      password: '', // 临时占位
      email: data.email,
      realName: data.realName,
      role: data.role,
      status: AdminStatus.ACTIVE,
      loginFailCount: 0,
      createdBy: data.createdBy
    });

    await admin.setPassword(data.password);
    await admin.save();
    
    return admin;
  }
}

// 初始化模型
AdminUser.init(
  {
    id: {
      type: DataTypes.INTEGER.UNSIGNED,
      autoIncrement: true,
      primaryKey: true,
    },
    username: {
      type: DataTypes.STRING(50),
      allowNull: false,
      unique: true,
      comment: '管理员用户名'
    },
    password: {
      type: DataTypes.STRING(255),
      allowNull: false,
      comment: '加密密码'
    },
    email: {
      type: DataTypes.STRING(100),
      allowNull: false,
      unique: true,
      validate: {
        isEmail: true
      },
      comment: '邮箱地址'
    },
    realName: {
      type: DataTypes.STRING(50),
      allowNull: true,
      comment: '真实姓名'
    },
    role: {
      type: DataTypes.ENUM(...Object.values(AdminRole)),
      allowNull: false,
      defaultValue: AdminRole.OPERATOR,
      comment: '管理员角色'
    },
    status: {
      type: DataTypes.ENUM(...Object.values(AdminStatus)),
      allowNull: false,
      defaultValue: AdminStatus.ACTIVE,
      comment: '账户状态'
    },
    lastLoginAt: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: '最后登录时间'
    },
    loginFailCount: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false,
      defaultValue: 0,
      comment: '登录失败次数'
    },
    lockedUntil: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: '锁定到期时间'
    },
    createdBy: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: true,
      comment: '创建者ID'
    }
  },
  {
    tableName: "admin_users",
    sequelize,
    timestamps: true,
    indexes: [
      {
        name: 'idx_username',
        fields: ['username']
      },
      {
        name: 'idx_email',
        fields: ['email']
      },
      {
        name: 'idx_status',
        fields: ['status']
      }
    ],
    comment: '管理员用户表'
  }
);