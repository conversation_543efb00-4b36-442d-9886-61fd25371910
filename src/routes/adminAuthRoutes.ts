import { Router } from 'express';
import { languageMiddleware } from '../middlewares/languageMiddleware';
import { 
  adminAuthMiddleware,
  adminRoleMiddleware,
  securityConfirmationMiddleware,
  adminRateLimitMiddleware
} from '../middlewares/adminAuth';
import {
  login,
  logout,
  refreshToken,
  getProfile,
  changePassword,
  getSessions
} from '../controllers/AdminAuthController';
import { AdminRole } from '../models/AdminUser';

const router = Router();

// 应用语言中间件到所有路由
router.use(languageMiddleware);

/**
 * 管理员登录
 * POST /api/admin/auth/login
 * 
 * Body:
 * {
 *   "username": "admin",
 *   "password": "password123"
 * }
 * 
 * Response:
 * {
 *   "success": true,
 *   "data": {
 *     "admin": { ... },
 *     "accessToken": "jwt_token",
 *     "refreshToken": "refresh_token",
 *     "expiresIn": "2h"
 *   }
 * }
 */
router.post('/login', adminRateLimitMiddleware(5, 300000), login); // 5分钟内最多5次登录尝试

/**
 * 刷新Token
 * POST /api/admin/auth/refresh
 * 
 * Body:
 * {
 *   "refreshToken": "refresh_jwt_token"
 * }
 * 
 * Response:
 * {
 *   "success": true,
 *   "data": {
 *     "accessToken": "new_jwt_token",
 *     "expiresIn": "2h"
 *   }
 * }
 */
router.post('/refresh', adminRateLimitMiddleware(10, 60000), refreshToken); // 1分钟内最多10次刷新

// 以下路由需要管理员认证
router.use(adminAuthMiddleware);

/**
 * 管理员登出
 * POST /api/admin/auth/logout
 * 
 * Headers:
 * Authorization: Bearer <access_token>
 * 
 * Response:
 * {
 *   "success": true,
 *   "data": {},
 *   "message": "登出成功"
 * }
 */
router.post('/logout', logout);

/**
 * 获取当前管理员信息
 * GET /api/admin/auth/profile
 * 
 * Headers:
 * Authorization: Bearer <access_token>
 * 
 * Response:
 * {
 *   "success": true,
 *   "data": {
 *     "admin": {
 *       "id": 1,
 *       "username": "admin",
 *       "email": "<EMAIL>",
 *       "realName": "管理员",
 *       "role": "super_admin",
 *       "status": "active",
 *       "lastLoginAt": "2023-12-01T10:00:00.000Z",
 *       "createdAt": "2023-11-01T10:00:00.000Z"
 *     }
 *   }
 * }
 */
router.get('/profile', getProfile);

/**
 * 修改密码
 * POST /api/admin/auth/change-password
 * 
 * Headers:
 * Authorization: Bearer <access_token>
 * 
 * Body:
 * {
 *   "oldPassword": "old_password",
 *   "newPassword": "new_password"
 * }
 * 
 * Response:
 * {
 *   "success": true,
 *   "data": {},
 *   "message": "密码修改成功，请重新登录"
 * }
 */
router.post('/change-password', 
  adminRateLimitMiddleware(3, 300000), // 5分钟内最多3次修改密码
  securityConfirmationMiddleware, 
  changePassword
);

/**
 * 获取当前管理员的会话列表
 * GET /api/admin/auth/sessions
 * 
 * Headers:
 * Authorization: Bearer <access_token>
 * 
 * Response:
 * {
 *   "success": true,
 *   "data": {
 *     "sessions": [
 *       {
 *         "id": 1,
 *         "ipAddress": "***********",
 *         "userAgent": "Mozilla/5.0...",
 *         "lastActivityAt": "2023-12-01T10:30:00.000Z",
 *         "createdAt": "2023-12-01T10:00:00.000Z",
 *         "isCurrent": true
 *       }
 *     ],
 *     "total": 1
 *   }
 * }
 */
router.get('/sessions', getSessions);

export default router;