// src/types/customRequest.ts
import { Request } from "express";
import { AdminRole } from "../models/AdminUser";

// 定义一个接口，但不做声明合并
export interface MyUserPayload {
  userId: number;
  walletId?: number;
  walletAddress?: string;
  telegramId?: string;
  isPremium?: boolean;
}

// 管理员用户信息接口
export interface AdminUserPayload {
  adminId: number;
  username: string;
  role: AdminRole;
  tokenId: string;
}

// 只是个类型，用来断言
export type MyRequest = Request & {
  user?: MyUserPayload
};

// 管理员请求类型
export type AdminRequest = Request & {
  admin?: AdminUserPayload;
};